{"diagnosis_time": "2025-07-31 00:53:35", "test_cases": {"ICNT-USDT_bybit_futures": {"symbol": "ICNT-USDT", "exchange": "bybit", "market_type": "futures", "test_amount": 153.307, "rule_found": true, "rule_details": {"qty_step": "0.001", "price_step": "0.01", "qty_precision": 3, "min_qty": "0.001", "max_qty": "1000000", "source": "default"}, "formatted_amount": "153.307", "precision_valid": true, "error": null}, "SPK-USDT_bybit_spot": {"symbol": "SPK-USDT", "exchange": "bybit", "market_type": "spot", "test_amount": 321.995, "rule_found": true, "rule_details": {"qty_step": "0.001", "price_step": "0.01", "qty_precision": 3, "min_qty": "0.001", "max_qty": "1000000", "source": "default"}, "formatted_amount": "321.995", "precision_valid": true, "error": null}}, "precision_analysis": {"ICNT-USDT_futures": {"original_amount": 153.307, "error_code": "10001", "error_message": "Qty invalid", "analysis": "数量153.307可能不符合Bybit期货的步长要求", "possible_causes": ["步长不是0.001的整数倍", "合约转换后的数量不正确", "精度处理有误"]}, "SPK-USDT_spot": {"original_amount": 321.995, "error_code": "170137", "error_message": "Order quantity has too many decimals", "analysis": "数量321.995小数位过多，超过了Bybit现货允许的精度", "possible_causes": ["小数位数超过basePrecision要求", "尾随零处理不正确", "精度截取逻辑有误"]}}, "fix_recommendations": [{"issue": "ICNT-USDT期货数量无效", "root_cause": "期货合约转换或步长处理不正确", "fix_steps": ["检查ICNT-USDT期货的实际步长要求", "验证format_amount_with_contract_conversion方法的合约转换逻辑", "确保数量是步长的整数倍", "添加Bybit期货特殊处理逻辑"], "code_location": "core/trading_rules_preloader.py:format_amount_with_contract_conversion", "priority": "HIGH"}, {"issue": "SPK-USDT现货小数位过多", "root_cause": "现货精度处理不符合Bybit basePrecision要求", "fix_steps": ["检查SPK-USDT现货的basePrecision设置", "修复format_amount_unified方法的小数位处理", "应用Bybit尾随零修复逻辑", "确保精度截取而非四舍五入"], "code_location": "core/trading_rules_preloader.py:format_amount_unified", "priority": "HIGH"}, {"issue": "通用精度处理不一致", "root_cause": "现货和期货使用不同的精度处理逻辑", "fix_steps": ["统一现货basePrecision和期货qtyStep的处理", "确保所有Bybit交易对都应用尾随零修复", "添加精度验证和错误处理", "实施统一的步长合规检查"], "code_location": "core/trading_rules_preloader.py:_get_bybit_trading_rule", "priority": "MEDIUM"}]}