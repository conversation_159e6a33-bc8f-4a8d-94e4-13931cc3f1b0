2025-07-31 01:44:34,045 [INFO] [__main__] 🚀 开始精确错误重现...
2025-07-31 01:44:34,045 [INFO] [__main__] 🔍 步骤1：正常初始化系统
2025-07-31 01:44:34,592 [INFO] [TelegramNotifier] TelegramNotifier初始化:
2025-07-31 01:44:34,592 [INFO] [TelegramNotifier]   Bot Token: 已设置
2025-07-31 01:44:34,593 [INFO] [TelegramNotifier]   Chat ID: 已设置
2025-07-31 01:44:34,593 [INFO] [TelegramNotifier]   Base URL: https://api.telegram.org/bot7954850653:AAHOIyX61SF1oDDxZBVclzfdmJhX-Jbn0-0/sendMessage
2025-07-31 01:44:34,594 [INFO] [TelegramNotifier] ✅ Telegram配置完整，通知功能已启用
2025-07-31 01:44:34,594 [INFO] [utils.notification] 已启用的通知器: ['telegram', 'console']
2025-07-31 01:44:34,624 [INFO] [core.trading_system_initializer] ✅ 交易系统初始化器创建完成
2025-07-31 01:44:34,624 [INFO] [__main__] 🔍 步骤2：使用真实交易所初始化
2025-07-31 01:44:34,625 [INFO] [core.trading_system_initializer] 🏪 独立初始化交易所...
2025-07-31 01:44:34,625 [INFO] [exchanges.exchanges_base] 初始化gate交易所接口，API请求限制: 10/秒
2025-07-31 01:44:34,625 [INFO] [exchanges.gate_exchange] 🔧 Gate API限制根源修复为8次/秒，确保30+代币健壮启动
2025-07-31 01:44:34,625 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 8/秒
2025-07-31 01:44:34,627 [INFO] [core.universal_token_system] 🚀 通用代币系统初始化: 支持10个代币
2025-07-31 01:44:34,627 [DEBUG] [core.universal_token_system] 支持的代币: ['SPK-USDT', 'RESOLV-USDT', 'ICNT-USDT', 'CAKE-USDT', 'WIF-USDT', 'AI16Z-USDT', 'SOL-USDT', 'MATIC-USDT', 'DOT-USDT', 'JUP-USDT']
2025-07-31 01:44:34,628 [INFO] [core.universal_token_system] ✅ 加载交易对缓存: 10个交易对
2025-07-31 01:44:34,629 [INFO] [core.universal_token_system] 📅 缓存时间: Thu Jul 31 00:55:45 2025
2025-07-31 01:44:34,629 [INFO] [core.universal_token_system] 📋 使用缓存的交易对: 10个
2025-07-31 01:44:34,630 [INFO] [core.trading_rules_preloader] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 01:44:34,630 [DEBUG] [core.trading_rules_preloader] 预加载交易对列表: ['SPK-USDT', 'RESOLV-USDT', 'ICNT-USDT', 'CAKE-USDT', 'WIF-USDT', 'AI16Z-USDT', 'SOL-USDT', 'MATIC-USDT', 'DOT-USDT', 'JUP-USDT']
2025-07-31 01:44:34,630 [INFO] [core.trading_rules_preloader] ✅ 交易规则预加载器初始化完成
2025-07-31 01:44:34,631 [INFO] [core.trading_rules_preloader]    缓存过期时间: 24小时
2025-07-31 01:44:34,631 [INFO] [core.trading_rules_preloader]    预加载交易对数量: 10
2025-07-31 01:44:34,631 [INFO] [core.unified_opening_manager] ✅ 统一开仓管理器初始化完成
2025-07-31 01:44:34,632 [INFO] [core.unified_opening_manager]    🔥 使用API动态精度，删除硬编码
2025-07-31 01:44:34,632 [INFO] [core.unified_opening_manager]    🔥 步长缓存机制，最高速度
2025-07-31 01:44:34,633 [INFO] [core.unified_opening_manager]    🔥 严格截断，不四舍五入
2025-07-31 01:44:34,633 [INFO] [core.unified_closing_manager] ✅ 统一平仓管理器初始化完成
2025-07-31 01:44:34,633 [INFO] [core.unified_closing_manager]    🔥 API精度+步长+缓存+重试机制
2025-07-31 01:44:34,634 [INFO] [core.unified_closing_manager]    🔥 严格截取，绝不四舍五入
2025-07-31 01:44:34,634 [INFO] [core.unified_closing_manager]    最大重试次数: 3
2025-07-31 01:44:34,634 [INFO] [core.unified_closing_manager]    重试精度序列: [6, 4, 2, 1]
2025-07-31 01:44:34,635 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 01:44:34,636 [INFO] [UnifiedHttpSessionManager] ✅ 统一HTTP会话管理器初始化完成
2025-07-31 01:44:34,636 [INFO] [exchanges.gate_exchange] 🚀 初始化Gate.io交易所（分离账户模式）...
2025-07-31 01:44:34,661 [WARNING] [UnifiedHttpSessionManager] ⚠️ SSL验证已禁用 - 仅适用于开发/测试环境
2025-07-31 01:44:34,661 [INFO] [config.network_config] ✅ 统一网络配置管理器初始化完成
2025-07-31 01:44:34,662 [INFO] [config.network_config]    🔥 连接超时: 5.0秒
2025-07-31 01:44:34,662 [INFO] [config.network_config]    🔥 总超时: 10.0秒
2025-07-31 01:44:34,663 [INFO] [config.network_config]    🔥 最大重试: 3次
2025-07-31 01:44:34,663 [INFO] [config.network_config]    🔥 重试延迟: 50ms
2025-07-31 01:44:34,679 [INFO] [UnifiedHttpSessionManager] ✅ 创建新HTTP会话: gate (总计: 1)
2025-07-31 01:44:35,147 [INFO] [exchanges.gate_exchange] ✅ Gate.io连接成功，服务器时间: ****************
2025-07-31 01:44:35,147 [INFO] [exchanges.gate_exchange] ✅ Gate.io使用分离账户模式
2025-07-31 01:44:35,149 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/spot/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '74d509b0607bb697ffc3bbd8d5a7062ec86a66dc763b416541887f13f892f3743b630d3158f17a6a216d937019ff363fa2abba5c416091c060fc91c914a3c36b', 'Timestamp': '**********.1486773', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:35,248 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): [{'currency': 'ICNT', 'available': '0.00425', 'locked': '0', 'update_id': 48}, {'currency': 'HUMA', 'available': '0.00087', 'locked': '0', 'update_id': 14}, {'currency': 'TMAI', 'available': '0.********', 'locked': '0', 'update_id': 53}, {'currency': 'DOGE', 'available': '0.00099', 'locked': '0', 'update_id': 4}, {'currency': 'USDT', 'available': '103.************', 'locked': '0', 'update_id': 1490}, {'currency': 'NEAR', 'available': '0.00799', 'locked': '0', 'update_id': 218}, {'currency': 'GT', 'available': '0.**********', 'locked': '0', 'update_id': 634}, {'currency': 'ALCH', 'available': '0.08', 'locked': '0', 'update_id': 26}, {'currency': 'SPK', 'available': '0.00943', 'locked': '0', 'update_id': 9}, {'currency': 'PEPE', 'available': '0.039', 'locked': '0', 'update_id': 6}, {'currency': '4EVER', 'available': '0.02703235', 'locked': '0', 'update_id': 26}, {'currency': 'GLS', 'available': '0.01086977', 'locked': '0', 'update_id': 219}, {'currency': 'BTC', 'available': '0.00000786', 'locked': '0', 'update_id': 93}, {'currency': 'UNI', 'available': '0.00952', 'locked': '0', 'update_id': 62}, {'currency': 'BNB', 'available': '0.000035', 'locked': '0', 'update_id': 20}, {'currency': 'ETH', 'available': '0.0000901', 'locked': '0', 'update_id': 6}, {'currency': 'RESOLV', 'available': '0.00609', 'locked': '0', 'update_id': 109}, {'currency': 'LINK', 'available': '0.00141', 'locked': '0', 'update_id': 12}, {'currency': 'PI', 'available': '0.00883', 'locked': '0', 'update_id': 4}, {'currency': 'LTC', 'available': '0.0000506', 'locked': '0', 'update_id': 25}, {'currency': 'POINT', 'available': '0.**********', 'locked': '0', 'update_id': 17}, {'currency': 'LAYER', 'available': '0', 'locked': '0', 'update_id': 0}, {'currency': 'ADA', 'available': '0.00216', 'locked': '0', 'update_id': 117}]
2025-07-31 01:44:35,249 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.399秒 (要求: 1.500秒)
2025-07-31 01:44:36,661 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '80a227de1e94ea166121e5692b1c2a2d916608838bb12bc49c7bee0302135009e6ed518b4e76a38af83856ad0a441ea1f1ed23cecc65a5fa7ce1ae473cee1c2f', 'Timestamp': '**********.6618884', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:36,728 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'order_margin': '0', 'point': '0', 'bonus': '0', 'history': {'dnw': '-124.********', 'pnl': '262.***********', 'refr': '0', 'point_fee': '0', 'fund': '1.***********', 'bonus_dnw': '0', 'point_refr': '0', 'bonus_offset': '0', 'fee': '-34.********', 'point_dnw': '0', 'cross_settle': '0'}, 'unrealised_pnl': '0', 'total': '104.***********', 'available': '104.***********', 'enable_credit': False, 'in_dual_mode': False, 'currency': 'USDT', 'position_margin': '0', 'user': ********, 'update_time': **********, 'update_id': 1158, 'position_initial_margin': '0', 'maintenance_margin': '0', 'margin_mode': 0, 'enable_evolved_classic': True, 'cross_initial_margin': '0', 'cross_maintenance_margin': '0', 'cross_order_margin': '0', 'cross_unrealised_pnl': '0', 'cross_virtual_unrealised_pnl': '0', 'cross_available': '104.***********', 'isolated_position_margin': '0', 'enable_new_dual_mode': False, 'margin_mode_name': 'classic', 'cross_margin_balance': '104.***********', 'cross_mmr': '0', 'cross_imr': '0', 'enable_tiered_mm': False, 'position_voucher_total': '0'}
2025-07-31 01:44:36,729 [INFO] [exchanges.gate_exchange] ✅ Gate.io初始余额: 现货103.19 + 期货104.64 = 总计207.83 USDT
2025-07-31 01:44:36,730 [INFO] [exchanges.gate_exchange] ✅ Gate.io分离账户模式已确认
2025-07-31 01:44:36,730 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所初始化完成
2025-07-31 01:44:36,731 [INFO] [core.trading_system_initializer] ✅ Gate.io初始化成功
2025-07-31 01:44:36,731 [INFO] [exchanges.exchanges_base] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 01:44:36,732 [INFO] [exchanges.bybit_exchange] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 01:44:36,732 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 01:44:36,733 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 01:44:36,737 [DEBUG] [BybitExchange] ✅ Bybit统一模块初始化完成
2025-07-31 01:44:36,739 [INFO] [config.network_config] 🔥 网络配置环境变量已应用
2025-07-31 01:44:36,739 [INFO] [core.universal_token_system] 📋 使用缓存的交易对: 10个
2025-07-31 01:44:36,740 [INFO] [root] ✅ 动态阈值系统已启用，无需传统阈值验证
2025-07-31 01:44:36,741 [DEBUG] [BybitExchange] ✅ Bybit配置加载完成: 杠杆=3x
2025-07-31 01:44:36,741 [INFO] [BybitExchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 01:44:36,742 [DEBUG] [BybitExchange] ✅ Bybit统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-31 01:44:36,742 [DEBUG] [exchanges.bybit_exchange] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 01:44:36,743 [INFO] [exchanges.bybit_exchange] 🚀 初始化Bybit交易所（统一账户模式）...
2025-07-31 01:44:36,768 [WARNING] [UnifiedHttpSessionManager] ⚠️ SSL验证已禁用 - 仅适用于开发/测试环境
2025-07-31 01:44:36,778 [INFO] [UnifiedHttpSessionManager] ✅ 创建新HTTP会话: bybit (总计: 2)
2025-07-31 01:44:36,778 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/time
2025-07-31 01:44:36,779 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:44:36,779 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {}
2025-07-31 01:44:37,592 [WARNING] [exchanges.bybit_exchange] Bybit服务器时间响应格式异常，使用本地时间
2025-07-31 01:44:37,593 [INFO] [exchanges.bybit_exchange] ✅ Bybit连接成功，服务器时间: *************
2025-07-31 01:44:37,593 [INFO] [exchanges.bybit_exchange] ✅ Bybit使用统一账户模式
2025-07-31 01:44:37,594 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-31 01:44:37,595 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 0.683秒 (要求: 1.500秒)
2025-07-31 01:44:38,293 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 1 → 1 参数
2025-07-31 01:44:38,588 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: *************875693 -> *************ms
2025-07-31 01:44:38,589 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: *************, 服务器时间: *************, 偏移: 4226ms
2025-07-31 01:44:38,590 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=*************, 偏移=4226, 调整后=*************
2025-07-31 01:44:38,590 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: *************
2025-07-31 01:44:38,591 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:44:38,591 [DEBUG] [exchanges.bybit_exchange]   timestamp: *************
2025-07-31 01:44:38,592 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:44:38,593 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:44:38,593 [DEBUG] [exchanges.bybit_exchange]   param_str: accountType=UNIFIED
2025-07-31 01:44:38,593 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 54
2025-07-31 01:44:38,594 [DEBUG] [exchanges.bybit_exchange]   生成的签名: a701427a83294f1b...
2025-07-31 01:44:38,594 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED
2025-07-31 01:44:38,595 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '*************', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': 'a701427a83294f1b70f073ec3352067e03f274cafa9969fcae9cf15ff423150b'}
2025-07-31 01:44:38,595 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'accountType': 'UNIFIED'}
2025-07-31 01:44:39,206 [DEBUG] [exchanges.bybit_exchange] Bybit原始余额响应: {'list': [{'accountIMRate': 0.0, 'totalMaintenanceMarginByMp': '0', 'totalInitialMargin': '0', 'accountType': 'UNIFIED', 'accountMMRate': 0.0, 'accountMMRateByMp': 0.0, 'accountIMRateByMp': 0.0, 'totalInitialMarginByMp': '0', 'totalMaintenanceMargin': '0', 'totalEquity': '525.********', 'totalMarginBalance': 521.********, 'totalAvailableBalance': 521.********, 'totalPerpUPL': '0', 'totalWalletBalance': 521.********, 'accountLTV': '0', 'coin': [{'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0.00984', 'totalPositionMM': '0', 'usdValue': 3.********, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.00984, 'cumRealisedPnl': '-0.00016', 'locked': '0', 'marginCollateral': False, 'coin': 'COINX'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0', 'totalPositionMM': '0', 'usdValue': 0.********, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.0, 'cumRealisedPnl': '-0.********', 'locked': '0', 'marginCollateral': True, 'coin': 'BTC'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '520.********', 'totalPositionMM': '0', 'usdValue': 521.********, 'unrealisedPnl': '0', 'collateralSwitch': True, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 520.********, 'cumRealisedPnl': '-47.09688583', 'locked': '0', 'marginCollateral': True, 'coin': 'USDT'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '1.13228612', 'totalPositionMM': '0', 'usdValue': 0.85718135, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 1.13228612, 'cumRealisedPnl': '0', 'locked': '0', 'marginCollateral': True, 'coin': 'MNT'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0.0324', 'totalPositionMM': '0', 'usdValue': 0.00366453, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.0324, 'cumRealisedPnl': '-4.0635', 'locked': '0', 'marginCollateral': True, 'coin': 'SPK'}]}]}
2025-07-31 01:44:39,207 [INFO] [exchanges.bybit_exchange] Bybit UNIFIED账户总可用余额: $521.05 USD
2025-07-31 01:44:39,208 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED COINX: 钱包=0.009840, 锁定=0.000000, 可用=0.009840
2025-07-31 01:44:39,208 [DEBUG] [exchanges.bybit_exchange] Bybit COINX 最终余额: 可用=0.009840, 锁定=0.000000
2025-07-31 01:44:39,208 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED BTC: 钱包=0.000000, 锁定=0.000000, 可用=0.000000
2025-07-31 01:44:39,209 [INFO] [exchanges.bybit_exchange] Bybit USDT余额详细计算: 钱包余额=520.99, 锁定=0.00, 订单保证金=0.00, 持仓保证金=0.00, 可用余额=520.99
2025-07-31 01:44:39,209 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED USDT: 钱包=520.986453, 锁定=0.000000, 可用=520.986453
2025-07-31 01:44:39,209 [DEBUG] [exchanges.bybit_exchange] Bybit USDT 最终余额: 可用=520.986453, 锁定=0.000000
2025-07-31 01:44:39,210 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED MNT: 钱包=1.132286, 锁定=0.000000, 可用=1.132286
2025-07-31 01:44:39,210 [DEBUG] [exchanges.bybit_exchange] Bybit MNT 最终余额: 可用=1.132286, 锁定=0.000000
2025-07-31 01:44:39,210 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED SPK: 钱包=0.032400, 锁定=0.000000, 可用=0.032400
2025-07-31 01:44:39,210 [DEBUG] [exchanges.bybit_exchange] Bybit SPK 最终余额: 可用=0.032400, 锁定=0.000000
2025-07-31 01:44:39,211 [INFO] [exchanges.bybit_exchange] Bybit账户余额处理完成: 4个币种
2025-07-31 01:44:39,211 [INFO] [exchanges.bybit_exchange] ✅ Bybit初始余额: 520.99 USDT
2025-07-31 01:44:39,211 [INFO] [exchanges.bybit_exchange] ✅ Bybit统一账户模式已激活
2025-07-31 01:44:39,211 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所初始化完成
2025-07-31 01:44:39,212 [INFO] [core.trading_system_initializer] ✅ Bybit初始化成功
2025-07-31 01:44:39,212 [INFO] [exchanges.exchanges_base] 初始化OKX交易所接口，API请求限制: 5/秒
2025-07-31 01:44:39,215 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-31 01:44:39,215 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-31 01:44:39,229 [WARNING] [UnifiedHttpSessionManager] ⚠️ SSL验证已禁用 - 仅适用于开发/测试环境
2025-07-31 01:44:39,237 [INFO] [UnifiedHttpSessionManager] ✅ 创建新HTTP会话: okx (总计: 3)
2025-07-31 01:44:39,657 [DEBUG] [exchanges.okx_exchange] OKX服务器时间同步: 偏移=4.531秒
2025-07-31 01:44:39,658 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:44:39,659 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:44.189Z
2025-07-31 01:44:39,659 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:44:39,824 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:44:39,824 [DEBUG] [exchanges.okx_exchange] OKX账户配置: acctLv=2, posMode=net_mode
2025-07-31 01:44:39,825 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-position-mode
2025-07-31 01:44:39,826 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:44.357Z
2025-07-31 01:44:39,827 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"posMode": "net_mode"}
2025-07-31 01:44:39,943 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'posMode': 'net_mode'}], 'msg': ''}
2025-07-31 01:44:39,944 [INFO] [core.universal_token_system] 📋 使用缓存的交易对: 10个
2025-07-31 01:44:39,944 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:44:39,945 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.380秒 (要求: 0.500秒)
2025-07-31 01:44:40,340 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:44:40,341 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:44.872Z
2025-07-31 01:44:40,341 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:44:40,461 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:44:40,462 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:44:40,463 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:44:40,464 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.377秒 (要求: 0.500秒)
2025-07-31 01:44:40,850 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:44:40,850 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:45.382Z
2025-07-31 01:44:40,851 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "SPK-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:44:40,974 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'SPK-USDT-SWAP', 'lever': '3', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:44:40,975 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:44:40,976 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:44:40,976 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:44:40,977 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.373秒 (要求: 0.500秒)
2025-07-31 01:44:41,348 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:44:41,349 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:45.879Z
2025-07-31 01:44:41,349 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:44:41,465 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:44:41,466 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:44:41,467 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:44:41,467 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.381秒 (要求: 0.500秒)
2025-07-31 01:44:41,859 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:44:41,859 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:46.390Z
2025-07-31 01:44:41,860 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "RESOLV-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:44:41,978 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'RESOLV-USDT-SWAP', 'lever': '3', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:44:41,978 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:44:41,979 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:44:41,979 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:44:41,980 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.379秒 (要求: 0.500秒)
2025-07-31 01:44:42,369 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:44:42,370 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:46.900Z
2025-07-31 01:44:42,370 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:44:42,491 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:44:42,491 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:44:42,492 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:44:42,493 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.377秒 (要求: 0.500秒)
2025-07-31 01:44:42,881 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:44:42,881 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:47.412Z
2025-07-31 01:44:42,882 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "ICNT-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:44:42,990 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:44:42,991 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:44:42,991 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:44:42,992 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:44:42,992 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/set-leverage
2025-07-31 01:44:42,993 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:44:42,993 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:44:42,993 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:44:42,994 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:44:42,995 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.387秒 (要求: 0.500秒)
2025-07-31 01:44:43,377 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:44:43,377 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:47.909Z
2025-07-31 01:44:43,378 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:44:43,498 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:44:43,499 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:44:43,500 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:44:43,500 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.378秒 (要求: 0.500秒)
2025-07-31 01:44:43,884 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:44:43,884 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:48.416Z
2025-07-31 01:44:43,885 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "CAKE-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:44:43,999 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:44:43,999 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:44:44,000 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:44:44,000 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:44:44,000 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/set-leverage
2025-07-31 01:44:44,001 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:44:44,002 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:44:44,002 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:44:44,003 [INFO] [core.trading_system_initializer] ✅ OKX初始化成功
2025-07-31 01:44:44,003 [INFO] [core.trading_system_initializer] ✅ 成功初始化 3 个交易所: ['gate', 'bybit', 'okx']
2025-07-31 01:44:44,004 [INFO] [core.trading_system_initializer] 🚀 初始化所有系统...
2025-07-31 01:44:44,005 [INFO] [core.trading_system_initializer] 🏪 独立初始化交易所...
2025-07-31 01:44:44,005 [INFO] [exchanges.exchanges_base] 初始化gate交易所接口，API请求限制: 10/秒
2025-07-31 01:44:44,006 [INFO] [exchanges.gate_exchange] 🔧 Gate API限制根源修复为8次/秒，确保30+代币健壮启动
2025-07-31 01:44:44,007 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 8/秒
2025-07-31 01:44:44,007 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 01:44:44,008 [INFO] [exchanges.gate_exchange] 🚀 初始化Gate.io交易所（分离账户模式）...
2025-07-31 01:44:44,008 [DEBUG] [UnifiedHttpSessionManager] 复用现有HTTP会话: gate
2025-07-31 01:44:44,067 [INFO] [exchanges.gate_exchange] ✅ Gate.io连接成功，服务器时间: ****************
2025-07-31 01:44:44,068 [INFO] [exchanges.gate_exchange] ✅ Gate.io使用分离账户模式
2025-07-31 01:44:44,068 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/spot/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '159064b467ac1e0198f94f2123261387ca906932582214f27bb2c2a372278abff38035ef3de4860f1b98931f79153160c90b898ebf3573d9c4a00cd1e28c2cf7', 'Timestamp': '**********.0687103', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:44,139 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): [{'currency': 'ICNT', 'available': '0.00425', 'locked': '0', 'update_id': 48}, {'currency': 'HUMA', 'available': '0.00087', 'locked': '0', 'update_id': 14}, {'currency': 'TMAI', 'available': '0.********', 'locked': '0', 'update_id': 53}, {'currency': 'DOGE', 'available': '0.00099', 'locked': '0', 'update_id': 4}, {'currency': 'USDT', 'available': '103.************', 'locked': '0', 'update_id': 1490}, {'currency': 'NEAR', 'available': '0.00799', 'locked': '0', 'update_id': 218}, {'currency': 'GT', 'available': '0.**********', 'locked': '0', 'update_id': 634}, {'currency': 'ALCH', 'available': '0.08', 'locked': '0', 'update_id': 26}, {'currency': 'SPK', 'available': '0.00943', 'locked': '0', 'update_id': 9}, {'currency': 'PEPE', 'available': '0.039', 'locked': '0', 'update_id': 6}, {'currency': '4EVER', 'available': '0.02703235', 'locked': '0', 'update_id': 26}, {'currency': 'GLS', 'available': '0.01086977', 'locked': '0', 'update_id': 219}, {'currency': 'BTC', 'available': '0.00000786', 'locked': '0', 'update_id': 93}, {'currency': 'UNI', 'available': '0.00952', 'locked': '0', 'update_id': 62}, {'currency': 'BNB', 'available': '0.000035', 'locked': '0', 'update_id': 20}, {'currency': 'ETH', 'available': '0.0000901', 'locked': '0', 'update_id': 6}, {'currency': 'RESOLV', 'available': '0.00609', 'locked': '0', 'update_id': 109}, {'currency': 'LINK', 'available': '0.00141', 'locked': '0', 'update_id': 12}, {'currency': 'PI', 'available': '0.00883', 'locked': '0', 'update_id': 4}, {'currency': 'LTC', 'available': '0.0000506', 'locked': '0', 'update_id': 25}, {'currency': 'POINT', 'available': '0.**********', 'locked': '0', 'update_id': 17}, {'currency': 'LAYER', 'available': '0', 'locked': '0', 'update_id': 0}, {'currency': 'ADA', 'available': '0.00216', 'locked': '0', 'update_id': 117}]
2025-07-31 01:44:44,141 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.427秒 (要求: 1.500秒)
2025-07-31 01:44:45,577 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'dc78a1f797c68690c26f39e9eb7bf082e915ce06a767e96a8b025a7d969aef49da9234d63b80c5e46a31370ec11829937083b7a40108fa1fbc580a03dd333a71', 'Timestamp': '**********.5773633', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:45,643 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'order_margin': '0', 'point': '0', 'bonus': '0', 'history': {'dnw': '-124.********', 'pnl': '262.***********', 'refr': '0', 'point_fee': '0', 'fund': '1.***********', 'bonus_dnw': '0', 'point_refr': '0', 'bonus_offset': '0', 'fee': '-34.********', 'point_dnw': '0', 'cross_settle': '0'}, 'unrealised_pnl': '0', 'total': '104.***********', 'available': '104.***********', 'enable_credit': False, 'in_dual_mode': False, 'currency': 'USDT', 'position_margin': '0', 'user': ********, 'update_time': **********, 'update_id': 1158, 'position_initial_margin': '0', 'maintenance_margin': '0', 'margin_mode': 0, 'enable_evolved_classic': True, 'cross_initial_margin': '0', 'cross_maintenance_margin': '0', 'cross_order_margin': '0', 'cross_unrealised_pnl': '0', 'cross_virtual_unrealised_pnl': '0', 'cross_available': '104.***********', 'isolated_position_margin': '0', 'enable_new_dual_mode': False, 'margin_mode_name': 'classic', 'cross_margin_balance': '104.***********', 'cross_mmr': '0', 'cross_imr': '0', 'enable_tiered_mm': False, 'position_voucher_total': '0'}
2025-07-31 01:44:45,644 [INFO] [exchanges.gate_exchange] ✅ Gate.io初始余额: 现货103.19 + 期货104.64 = 总计207.83 USDT
2025-07-31 01:44:45,644 [INFO] [exchanges.gate_exchange] ✅ Gate.io分离账户模式已确认
2025-07-31 01:44:45,645 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所初始化完成
2025-07-31 01:44:45,646 [INFO] [core.trading_system_initializer] ✅ Gate.io初始化成功
2025-07-31 01:44:45,646 [INFO] [exchanges.exchanges_base] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 01:44:45,647 [INFO] [exchanges.bybit_exchange] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 01:44:45,647 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 01:44:45,648 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 01:44:45,649 [DEBUG] [BybitExchange] ✅ Bybit统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-31 01:44:45,649 [DEBUG] [exchanges.bybit_exchange] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 01:44:45,650 [INFO] [exchanges.bybit_exchange] 🚀 初始化Bybit交易所（统一账户模式）...
2025-07-31 01:44:45,650 [DEBUG] [UnifiedHttpSessionManager] 复用现有HTTP会话: bybit
2025-07-31 01:44:45,651 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/time
2025-07-31 01:44:45,651 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:44:45,652 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {}
2025-07-31 01:44:46,255 [WARNING] [exchanges.bybit_exchange] Bybit服务器时间响应格式异常，使用本地时间
2025-07-31 01:44:46,256 [INFO] [exchanges.bybit_exchange] ✅ Bybit连接成功，服务器时间: *************
2025-07-31 01:44:46,256 [INFO] [exchanges.bybit_exchange] ✅ Bybit使用统一账户模式
2025-07-31 01:44:46,256 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-31 01:44:46,257 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 0.894秒 (要求: 1.500秒)
2025-07-31 01:44:47,158 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 1 → 1 参数
2025-07-31 01:44:47,464 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: *************608484 -> *************ms
2025-07-31 01:44:47,464 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: *************, 服务器时间: *************, 偏移: 4226ms
2025-07-31 01:44:47,465 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=*************, 偏移=4226, 调整后=*************
2025-07-31 01:44:47,465 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: *************
2025-07-31 01:44:47,466 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:44:47,466 [DEBUG] [exchanges.bybit_exchange]   timestamp: *************
2025-07-31 01:44:47,466 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:44:47,466 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:44:47,467 [DEBUG] [exchanges.bybit_exchange]   param_str: accountType=UNIFIED
2025-07-31 01:44:47,467 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 54
2025-07-31 01:44:47,467 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 493326f08d744456...
2025-07-31 01:44:47,467 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED
2025-07-31 01:44:47,468 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '*************', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '493326f08d7444568c5c9faacbabb85c8e405f188293f62a9dbb527fe5eb1120'}
2025-07-31 01:44:47,468 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'accountType': 'UNIFIED'}
2025-07-31 01:44:48,078 [DEBUG] [exchanges.bybit_exchange] Bybit原始余额响应: {'list': [{'accountIMRate': 0.0, 'totalMaintenanceMarginByMp': '0', 'totalInitialMargin': '0', 'accountType': 'UNIFIED', 'accountMMRate': 0.0, 'accountMMRateByMp': 0.0, 'accountIMRateByMp': 0.0, 'totalInitialMarginByMp': '0', 'totalMaintenanceMargin': '0', 'totalEquity': '525.********', 'totalMarginBalance': 521.********, 'totalAvailableBalance': 521.********, 'totalPerpUPL': '0', 'totalWalletBalance': 521.********, 'accountLTV': '0', 'coin': [{'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0.00984', 'totalPositionMM': '0', 'usdValue': 3.********, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.00984, 'cumRealisedPnl': '-0.00016', 'locked': '0', 'marginCollateral': False, 'coin': 'COINX'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0', 'totalPositionMM': '0', 'usdValue': 0.********, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.0, 'cumRealisedPnl': '-0.********', 'locked': '0', 'marginCollateral': True, 'coin': 'BTC'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '520.********', 'totalPositionMM': '0', 'usdValue': 521.********, 'unrealisedPnl': '0', 'collateralSwitch': True, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 520.********, 'cumRealisedPnl': '-47.09688583', 'locked': '0', 'marginCollateral': True, 'coin': 'USDT'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '1.13228612', 'totalPositionMM': '0', 'usdValue': 0.85717003, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 1.13228612, 'cumRealisedPnl': '0', 'locked': '0', 'marginCollateral': True, 'coin': 'MNT'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0.0324', 'totalPositionMM': '0', 'usdValue': 0.00366382, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.0324, 'cumRealisedPnl': '-4.0635', 'locked': '0', 'marginCollateral': True, 'coin': 'SPK'}]}]}
2025-07-31 01:44:48,080 [INFO] [exchanges.bybit_exchange] Bybit UNIFIED账户总可用余额: $521.05 USD
2025-07-31 01:44:48,081 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED COINX: 钱包=0.009840, 锁定=0.000000, 可用=0.009840
2025-07-31 01:44:48,081 [DEBUG] [exchanges.bybit_exchange] Bybit COINX 最终余额: 可用=0.009840, 锁定=0.000000
2025-07-31 01:44:48,082 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED BTC: 钱包=0.000000, 锁定=0.000000, 可用=0.000000
2025-07-31 01:44:48,082 [INFO] [exchanges.bybit_exchange] Bybit USDT余额详细计算: 钱包余额=520.99, 锁定=0.00, 订单保证金=0.00, 持仓保证金=0.00, 可用余额=520.99
2025-07-31 01:44:48,083 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED USDT: 钱包=520.986453, 锁定=0.000000, 可用=520.986453
2025-07-31 01:44:48,083 [DEBUG] [exchanges.bybit_exchange] Bybit USDT 最终余额: 可用=520.986453, 锁定=0.000000
2025-07-31 01:44:48,084 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED MNT: 钱包=1.132286, 锁定=0.000000, 可用=1.132286
2025-07-31 01:44:48,084 [DEBUG] [exchanges.bybit_exchange] Bybit MNT 最终余额: 可用=1.132286, 锁定=0.000000
2025-07-31 01:44:48,085 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED SPK: 钱包=0.032400, 锁定=0.000000, 可用=0.032400
2025-07-31 01:44:48,086 [DEBUG] [exchanges.bybit_exchange] Bybit SPK 最终余额: 可用=0.032400, 锁定=0.000000
2025-07-31 01:44:48,086 [INFO] [exchanges.bybit_exchange] Bybit账户余额处理完成: 4个币种
2025-07-31 01:44:48,086 [INFO] [exchanges.bybit_exchange] ✅ Bybit初始余额: 520.99 USDT
2025-07-31 01:44:48,087 [INFO] [exchanges.bybit_exchange] ✅ Bybit统一账户模式已激活
2025-07-31 01:44:48,087 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所初始化完成
2025-07-31 01:44:48,088 [INFO] [core.trading_system_initializer] ✅ Bybit初始化成功
2025-07-31 01:44:48,088 [INFO] [exchanges.exchanges_base] 初始化OKX交易所接口，API请求限制: 5/秒
2025-07-31 01:44:48,090 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-31 01:44:48,091 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-31 01:44:48,091 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-31 01:44:48,092 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-31 01:44:48,093 [DEBUG] [UnifiedHttpSessionManager] 复用现有HTTP会话: okx
2025-07-31 01:44:48,204 [DEBUG] [exchanges.okx_exchange] OKX服务器时间同步: 偏移=4.380秒
2025-07-31 01:44:48,206 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:44:48,206 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:52.585Z
2025-07-31 01:44:48,206 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:44:48,330 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:44:48,330 [DEBUG] [exchanges.okx_exchange] OKX账户配置: acctLv=2, posMode=net_mode
2025-07-31 01:44:48,331 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-31 01:44:48,331 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-31 01:44:48,332 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.261秒 (要求: 0.500秒)
2025-07-31 01:44:48,604 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-position-mode
2025-07-31 01:44:48,604 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:52.984Z
2025-07-31 01:44:48,605 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"posMode": "net_mode"}
2025-07-31 01:44:48,723 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'posMode': 'net_mode'}], 'msg': ''}
2025-07-31 01:44:48,724 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-31 01:44:48,724 [INFO] [core.universal_token_system] 📋 使用缓存的交易对: 10个
2025-07-31 01:44:48,725 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:44:48,726 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.379秒 (要求: 0.500秒)
2025-07-31 01:44:49,115 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:44:49,115 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:53.495Z
2025-07-31 01:44:49,116 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:44:49,228 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:44:49,229 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:44:49,229 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:44:49,230 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.385秒 (要求: 0.500秒)
2025-07-31 01:44:49,617 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:44:49,618 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:53.997Z
2025-07-31 01:44:49,618 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "SPK-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:44:49,740 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'SPK-USDT-SWAP', 'lever': '3', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:44:49,741 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:44:49,742 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:44:49,742 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-31 01:44:49,742 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:44:49,743 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.374秒 (要求: 0.500秒)
2025-07-31 01:44:50,129 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:44:50,130 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:54.509Z
2025-07-31 01:44:50,130 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:44:50,247 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:44:50,248 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:44:50,249 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:44:50,249 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.380秒 (要求: 0.500秒)
2025-07-31 01:44:50,643 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:44:50,644 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:55.023Z
2025-07-31 01:44:50,644 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "RESOLV-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:44:50,794 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'RESOLV-USDT-SWAP', 'lever': '3', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:44:50,795 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:44:50,795 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:44:50,796 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-31 01:44:50,797 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:44:50,797 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.346秒 (要求: 0.500秒)
2025-07-31 01:44:51,155 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:44:51,155 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:55.535Z
2025-07-31 01:44:51,157 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:44:51,264 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:44:51,265 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:44:51,265 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:44:51,266 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.389秒 (要求: 0.500秒)
2025-07-31 01:44:51,668 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:44:51,669 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:56.047Z
2025-07-31 01:44:51,669 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "ICNT-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:44:51,785 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:44:51,786 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:44:51,786 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:44:51,786 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:44:51,787 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/set-leverage
2025-07-31 01:44:51,788 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:44:51,788 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:44:51,788 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:44:51,789 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-31 01:44:51,789 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:44:51,790 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.378秒 (要求: 0.500秒)
2025-07-31 01:44:52,181 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:44:52,182 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:56.561Z
2025-07-31 01:44:52,182 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:44:52,309 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:44:52,310 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:44:52,311 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:44:52,311 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.370秒 (要求: 0.500秒)
2025-07-31 01:44:52,692 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:44:52,693 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:57.072Z
2025-07-31 01:44:52,693 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "CAKE-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:44:52,819 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:44:52,820 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:44:52,820 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:44:52,820 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:44:52,821 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/set-leverage
2025-07-31 01:44:52,821 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:44:52,821 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:44:52,822 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:44:52,823 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-31 01:44:52,823 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-31 01:44:52,824 [INFO] [core.trading_system_initializer] ✅ OKX初始化成功
2025-07-31 01:44:52,824 [INFO] [core.trading_system_initializer] ✅ 成功初始化 3 个交易所: ['gate', 'bybit', 'okx']
2025-07-31 01:44:52,824 [INFO] [core.trading_system_initializer] 📋 步骤1.5: 设置全局交易所实例...
2025-07-31 01:44:52,825 [INFO] [core.trading_system_initializer] ✅ 全局交易所实例已设置: ['gate', 'bybit', 'okx']
2025-07-31 01:44:52,826 [INFO] [core.trading_system_initializer] 📋 步骤2: 完成所有REST API调用...
2025-07-31 01:44:52,826 [INFO] [core.trading_system_initializer] 📋 开始预加载交易规则（带超时保护）...
2025-07-31 01:44:52,827 [INFO] [core.trading_system_initializer] 📋 预加载交易规则...
2025-07-31 01:44:52,827 [INFO] [core.trading_rules_preloader] 🚀 开始预加载所有交易规则...
2025-07-31 01:44:52,829 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_SPK-USDT_spot
2025-07-31 01:44:52,829 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_SPK-USDT_futures
2025-07-31 01:44:52,830 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_RESOLV-USDT_spot
2025-07-31 01:44:52,831 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_RESOLV-USDT_futures
2025-07-31 01:44:52,831 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_ICNT-USDT_spot
2025-07-31 01:44:52,832 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_ICNT-USDT_futures
2025-07-31 01:44:52,832 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_CAKE-USDT_spot
2025-07-31 01:44:52,833 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_CAKE-USDT_futures
2025-07-31 01:44:52,834 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_WIF-USDT_spot
2025-07-31 01:44:52,834 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_WIF-USDT_futures
2025-07-31 01:44:52,835 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_AI16Z-USDT_spot
2025-07-31 01:44:52,835 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_AI16Z-USDT_futures
2025-07-31 01:44:52,836 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_SOL-USDT_spot
2025-07-31 01:44:52,837 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_SOL-USDT_futures
2025-07-31 01:44:52,837 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_MATIC-USDT_spot
2025-07-31 01:44:52,838 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_MATIC-USDT_futures
2025-07-31 01:44:52,838 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_DOT-USDT_spot
2025-07-31 01:44:52,839 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_DOT-USDT_futures
2025-07-31 01:44:52,840 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_JUP-USDT_spot
2025-07-31 01:44:52,840 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: gate_JUP-USDT_futures
2025-07-31 01:44:52,841 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_SPK-USDT_spot
2025-07-31 01:44:52,841 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_SPK-USDT_futures
2025-07-31 01:44:52,842 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_RESOLV-USDT_spot
2025-07-31 01:44:52,843 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_RESOLV-USDT_futures
2025-07-31 01:44:52,843 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_ICNT-USDT_spot
2025-07-31 01:44:52,844 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_ICNT-USDT_futures
2025-07-31 01:44:52,844 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_CAKE-USDT_spot
2025-07-31 01:44:52,845 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_CAKE-USDT_futures
2025-07-31 01:44:52,846 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_WIF-USDT_spot
2025-07-31 01:44:52,846 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_WIF-USDT_futures
2025-07-31 01:44:52,847 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_AI16Z-USDT_spot
2025-07-31 01:44:52,848 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_AI16Z-USDT_futures
2025-07-31 01:44:52,848 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_SOL-USDT_spot
2025-07-31 01:44:52,849 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_SOL-USDT_futures
2025-07-31 01:44:52,849 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_MATIC-USDT_spot
2025-07-31 01:44:52,850 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_MATIC-USDT_futures
2025-07-31 01:44:52,850 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_DOT-USDT_spot
2025-07-31 01:44:52,851 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_DOT-USDT_futures
2025-07-31 01:44:52,851 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_JUP-USDT_spot
2025-07-31 01:44:52,852 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: bybit_JUP-USDT_futures
2025-07-31 01:44:52,852 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_SPK-USDT_spot
2025-07-31 01:44:52,853 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_SPK-USDT_futures
2025-07-31 01:44:52,854 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_RESOLV-USDT_spot
2025-07-31 01:44:52,854 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_RESOLV-USDT_futures
2025-07-31 01:44:52,854 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_ICNT-USDT_spot
2025-07-31 01:44:52,855 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_ICNT-USDT_futures
2025-07-31 01:44:52,856 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_CAKE-USDT_spot
2025-07-31 01:44:52,856 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_CAKE-USDT_futures
2025-07-31 01:44:52,857 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_WIF-USDT_spot
2025-07-31 01:44:52,857 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_WIF-USDT_futures
2025-07-31 01:44:52,858 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_AI16Z-USDT_spot
2025-07-31 01:44:52,858 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_AI16Z-USDT_futures
2025-07-31 01:44:52,859 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_SOL-USDT_spot
2025-07-31 01:44:52,859 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_SOL-USDT_futures
2025-07-31 01:44:52,860 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_MATIC-USDT_spot
2025-07-31 01:44:52,860 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_MATIC-USDT_futures
2025-07-31 01:44:52,861 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_DOT-USDT_spot
2025-07-31 01:44:52,861 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_DOT-USDT_futures
2025-07-31 01:44:52,862 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_JUP-USDT_spot
2025-07-31 01:44:52,862 [INFO] [core.trading_rules_preloader] ✅ 异步加载交易规则成功: okx_JUP-USDT_futures
2025-07-31 01:44:52,863 [INFO] [core.trading_rules_preloader] ✅ 交易规则预加载完成:
2025-07-31 01:44:52,863 [INFO] [core.trading_rules_preloader]    总任务数: 60
2025-07-31 01:44:52,864 [INFO] [core.trading_rules_preloader]    成功加载: 120
2025-07-31 01:44:52,864 [INFO] [core.trading_rules_preloader]    失败加载: 0
2025-07-31 01:44:52,864 [INFO] [core.trading_rules_preloader]    成功率: 200.0%
2025-07-31 01:44:52,865 [INFO] [core.trading_rules_preloader]    耗时: 35.8ms
2025-07-31 01:44:52,865 [INFO] [core.trading_rules_preloader]    缓存规则数: 60
2025-07-31 01:44:52,866 [INFO] [core.trading_rules_preloader]    不支持交易对数: 0
2025-07-31 01:44:52,866 [INFO] [core.trading_rules_preloader] 🎯 期现套利支持情况:
2025-07-31 01:44:52,867 [INFO] [core.trading_rules_preloader]    支持套利的代币数: 10
2025-07-31 01:44:52,867 [INFO] [core.trading_rules_preloader]    SPK-USDT: 6个组合 - gate现货-bybit期货, bybit现货-gate期货, okx现货-bybit期货, bybit现货-okx期货, okx现货-gate期货, gate现货-okx期货
2025-07-31 01:44:52,868 [INFO] [core.trading_rules_preloader]    RESOLV-USDT: 6个组合 - gate现货-bybit期货, bybit现货-gate期货, okx现货-bybit期货, bybit现货-okx期货, okx现货-gate期货, gate现货-okx期货
2025-07-31 01:44:52,868 [INFO] [core.trading_rules_preloader]    ICNT-USDT: 6个组合 - gate现货-bybit期货, bybit现货-gate期货, okx现货-bybit期货, bybit现货-okx期货, okx现货-gate期货, gate现货-okx期货
2025-07-31 01:44:52,869 [INFO] [core.trading_rules_preloader]    CAKE-USDT: 6个组合 - gate现货-bybit期货, bybit现货-gate期货, okx现货-bybit期货, bybit现货-okx期货, okx现货-gate期货, gate现货-okx期货
2025-07-31 01:44:52,869 [INFO] [core.trading_rules_preloader]    WIF-USDT: 6个组合 - gate现货-bybit期货, bybit现货-gate期货, okx现货-bybit期货, bybit现货-okx期货, okx现货-gate期货, gate现货-okx期货
2025-07-31 01:44:52,870 [INFO] [core.trading_rules_preloader]    AI16Z-USDT: 6个组合 - gate现货-bybit期货, bybit现货-gate期货, okx现货-bybit期货, bybit现货-okx期货, okx现货-gate期货, gate现货-okx期货
2025-07-31 01:44:52,870 [INFO] [core.trading_rules_preloader]    SOL-USDT: 6个组合 - gate现货-bybit期货, bybit现货-gate期货, okx现货-bybit期货, bybit现货-okx期货, okx现货-gate期货, gate现货-okx期货
2025-07-31 01:44:52,871 [INFO] [core.trading_rules_preloader]    MATIC-USDT: 6个组合 - gate现货-bybit期货, bybit现货-gate期货, okx现货-bybit期货, bybit现货-okx期货, okx现货-gate期货, gate现货-okx期货
2025-07-31 01:44:52,871 [INFO] [core.trading_rules_preloader]    DOT-USDT: 6个组合 - gate现货-bybit期货, bybit现货-gate期货, okx现货-bybit期货, bybit现货-okx期货, okx现货-gate期货, gate现货-okx期货
2025-07-31 01:44:52,872 [INFO] [core.trading_rules_preloader]    JUP-USDT: 6个组合 - gate现货-bybit期货, bybit现货-gate期货, okx现货-bybit期货, bybit现货-okx期货, okx现货-gate期货, gate现货-okx期货
2025-07-31 01:44:52,872 [INFO] [core.trading_rules_preloader] 🔥 开始执行缓存预热...
2025-07-31 01:44:52,873 [INFO] [core.trading_rules_preloader] 🔥 开始预热所有缓存系统...
2025-07-31 01:44:52,873 [INFO] [core.universal_token_system] 📋 使用缓存的交易对: 10个
2025-07-31 01:44:52,874 [INFO] [core.trading_rules_preloader] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 01:44:52,874 [DEBUG] [core.trading_rules_preloader] 预加载交易对列表: ['SPK-USDT', 'RESOLV-USDT', 'ICNT-USDT', 'CAKE-USDT', 'WIF-USDT', 'AI16Z-USDT', 'SOL-USDT', 'MATIC-USDT', 'DOT-USDT', 'JUP-USDT']
2025-07-31 01:44:52,875 [INFO] [CacheMonitor] 🚀 5大缓存系统监控器启动
2025-07-31 01:44:52,875 [INFO] [CacheMonitor] ================================================================================
2025-07-31 01:44:52,876 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,876 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,877 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,877 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,878 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,879 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,879 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,879 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,880 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,880 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,881 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,881 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,881 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,882 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,882 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,883 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,883 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,883 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,884 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,884 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,884 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,884 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,885 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,885 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,885 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate WIF-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,886 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate WIF-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,886 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit WIF-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,886 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit WIF-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,886 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx WIF-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,887 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx WIF-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,887 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate AI16Z-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,887 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate AI16Z-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,888 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit AI16Z-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,888 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit AI16Z-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,888 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx AI16Z-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,889 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx AI16Z-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,889 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate SOL-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,890 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate SOL-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,890 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit SOL-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,890 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit SOL-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,890 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx SOL-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,891 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx SOL-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,891 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate MATIC-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,891 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate MATIC-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,892 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit MATIC-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,892 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit MATIC-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,892 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx MATIC-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,892 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx MATIC-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,893 [INFO] [core.trading_rules_preloader] 📋 交易规则缓存预热完成: 48个成功
2025-07-31 01:44:52,893 [INFO] [core.universal_token_system] 📋 使用缓存的交易对: 10个
2025-07-31 01:44:52,893 [INFO] [core.trading_rules_preloader] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 01:44:52,894 [DEBUG] [core.trading_rules_preloader] 预加载交易对列表: ['SPK-USDT', 'RESOLV-USDT', 'ICNT-USDT', 'CAKE-USDT', 'WIF-USDT', 'AI16Z-USDT', 'SOL-USDT', 'MATIC-USDT', 'DOT-USDT', 'JUP-USDT']
2025-07-31 01:44:52,894 [INFO] [core.universal_token_system] 📋 使用缓存的交易对: 10个
2025-07-31 01:44:52,895 [INFO] [core.trading_rules_preloader] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 01:44:52,895 [DEBUG] [core.trading_rules_preloader] 预加载交易对列表: ['SPK-USDT', 'RESOLV-USDT', 'ICNT-USDT', 'CAKE-USDT', 'WIF-USDT', 'AI16Z-USDT', 'SOL-USDT', 'MATIC-USDT', 'DOT-USDT', 'JUP-USDT']
2025-07-31 01:44:52,895 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,896 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate SPK-USDT spot 格式化=100.0000
2025-07-31 01:44:52,896 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,896 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,896 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate SPK-USDT futures 格式化=100.0000
2025-07-31 01:44:52,897 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,897 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,897 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,897 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit SPK-USDT spot 格式化=100
2025-07-31 01:44:52,898 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,898 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,898 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,899 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit SPK-USDT futures 格式化=100
2025-07-31 01:44:52,899 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,900 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,900 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx SPK-USDT spot 格式化=100.00000
2025-07-31 01:44:52,900 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,901 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,901 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx SPK-USDT futures 格式化=100.00000
2025-07-31 01:44:52,901 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,901 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,902 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate RESOLV-USDT spot 格式化=100.0000
2025-07-31 01:44:52,902 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,902 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,902 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate RESOLV-USDT futures 格式化=100.0000
2025-07-31 01:44:52,903 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,903 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,904 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,904 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit RESOLV-USDT spot 格式化=100
2025-07-31 01:44:52,904 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,905 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,905 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,905 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit RESOLV-USDT futures 格式化=100
2025-07-31 01:44:52,905 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,907 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,907 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx RESOLV-USDT spot 格式化=100.00000
2025-07-31 01:44:52,907 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,907 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,908 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx RESOLV-USDT futures 格式化=100.00000
2025-07-31 01:44:52,908 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,908 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,908 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate ICNT-USDT spot 格式化=100.0000
2025-07-31 01:44:52,909 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,909 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,909 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate ICNT-USDT futures 格式化=100.0000
2025-07-31 01:44:52,910 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,910 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,911 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,911 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit ICNT-USDT spot 格式化=100
2025-07-31 01:44:52,911 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,911 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,912 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,912 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit ICNT-USDT futures 格式化=100
2025-07-31 01:44:52,912 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,912 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,913 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx ICNT-USDT spot 格式化=100.00000
2025-07-31 01:44:52,913 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,913 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,913 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx ICNT-USDT futures 格式化=100.00000
2025-07-31 01:44:52,913 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,914 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,914 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate CAKE-USDT spot 格式化=100.0000
2025-07-31 01:44:52,915 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,915 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,915 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate CAKE-USDT futures 格式化=100.0000
2025-07-31 01:44:52,915 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,916 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,916 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,916 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit CAKE-USDT spot 格式化=100
2025-07-31 01:44:52,917 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,917 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,918 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,918 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit CAKE-USDT futures 格式化=100
2025-07-31 01:44:52,918 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,918 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,919 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx CAKE-USDT spot 格式化=100.00000
2025-07-31 01:44:52,919 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,919 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,919 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx CAKE-USDT futures 格式化=100.00000
2025-07-31 01:44:52,920 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,920 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate WIF-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,920 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate WIF-USDT spot 格式化=100.0000
2025-07-31 01:44:52,920 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,921 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate WIF-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,921 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate WIF-USDT futures 格式化=100.0000
2025-07-31 01:44:52,921 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,921 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit WIF-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,922 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,922 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit WIF-USDT spot 格式化=100
2025-07-31 01:44:52,922 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,922 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit WIF-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,922 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,923 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit WIF-USDT futures 格式化=100
2025-07-31 01:44:52,923 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,923 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx WIF-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,923 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx WIF-USDT spot 格式化=100.00000
2025-07-31 01:44:52,924 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,924 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx WIF-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,924 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx WIF-USDT futures 格式化=100.00000
2025-07-31 01:44:52,924 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,925 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate AI16Z-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,925 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate AI16Z-USDT spot 格式化=100.0000
2025-07-31 01:44:52,925 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,925 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate AI16Z-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,925 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: gate AI16Z-USDT futures 格式化=100.0000
2025-07-31 01:44:52,925 [DEBUG] [core.trading_rules_preloader] 🔥 gate预加载器格式化: 100.00000000 → '100.0000' (步长=0.0001)
2025-07-31 01:44:52,925 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit AI16Z-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,926 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,926 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit AI16Z-USDT spot 格式化=100
2025-07-31 01:44:52,926 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,926 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit AI16Z-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,927 [DEBUG] [core.trading_rules_preloader] 🔧 Bybit尾随零修复: 原始=100.000 → 修复后=100
2025-07-31 01:44:52,927 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: bybit AI16Z-USDT futures 格式化=100
2025-07-31 01:44:52,927 [DEBUG] [core.trading_rules_preloader] 🔥 bybit预加载器格式化: 100.00000000 → '100' (步长=0.001)
2025-07-31 01:44:52,927 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx AI16Z-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,928 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx AI16Z-USDT spot 格式化=100.00000
2025-07-31 01:44:52,928 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,928 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx AI16Z-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,928 [INFO] [CacheMonitor] 🎯 [精度缓存] 命中: okx AI16Z-USDT futures 格式化=100.00000
2025-07-31 01:44:52,929 [DEBUG] [core.trading_rules_preloader] 🔥 okx预加载器格式化: 100.00000000 → '100.00000' (步长=0.00001)
2025-07-31 01:44:52,929 [INFO] [core.trading_rules_preloader] 🎯 精度缓存预热完成: 36个成功
2025-07-31 01:44:52,929 [INFO] [core.universal_token_system] 📋 使用缓存的交易对: 10个
2025-07-31 01:44:52,929 [INFO] [core.trading_rules_preloader] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 01:44:52,930 [DEBUG] [core.trading_rules_preloader] 预加载交易对列表: ['SPK-USDT', 'RESOLV-USDT', 'ICNT-USDT', 'CAKE-USDT', 'WIF-USDT', 'AI16Z-USDT', 'SOL-USDT', 'MATIC-USDT', 'DOT-USDT', 'JUP-USDT']
2025-07-31 01:44:52,930 [INFO] [utils.margin_calculator] 🔍 获取合约信息: gate SPK-USDT (尝试 1/3)
2025-07-31 01:44:52,930 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: SPK_USDT
2025-07-31 01:44:52,931 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/SPK_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '76cc94a68d317292d1c63c171827b252556a8a5769638c67d85ab223f88bb0549880934215ea20c8e70d91589c17eacda3560db8bac33e320e90b6362a6d6347', 'Timestamp': '1753897492.9319978', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:52,938 [INFO] [UnifiedBalanceManager] ✅ 统一余额管理器初始化 - 解决重复余额获取问题
2025-07-31 01:44:52,939 [INFO] [UnifiedBalanceManager] 🔍 [API调用] 余额接口: 统一更新 | 缓存过期，真实API查询余额
2025-07-31 01:44:52,939 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.493秒 (要求: 1.500秒)
2025-07-31 01:44:52,947 [INFO] [core.unified_leverage_manager] 统一杠杆管理器初始化: {'gate': 3, 'bybit': 3, 'okx': 3}
2025-07-31 01:44:52,947 [INFO] [core.unified_leverage_manager] 🔥 杠杆缓存系统已启用 - TTL: 5分钟
2025-07-31 01:44:52,948 [INFO] [core.universal_token_system] 📋 使用缓存的交易对: 10个
2025-07-31 01:44:52,948 [INFO] [core.trading_rules_preloader] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 01:44:52,948 [DEBUG] [core.trading_rules_preloader] 预加载交易对列表: ['SPK-USDT', 'RESOLV-USDT', 'ICNT-USDT', 'CAKE-USDT', 'WIF-USDT', 'AI16Z-USDT', 'SOL-USDT', 'MATIC-USDT', 'DOT-USDT', 'JUP-USDT']
2025-07-31 01:44:52,949 [INFO] [core.unified_leverage_manager] 🔥 开始杠杆缓存预热...
2025-07-31 01:44:52,949 [INFO] [core.unified_leverage_manager] 🔧 Gate统一杠杆设置: SPK-USDT 3倍
2025-07-31 01:44:52,949 [INFO] [core.unified_leverage_manager] 🔧 Gate.io杠杆设置: 合约=SPK_USDT, 杠杆=3倍
2025-07-31 01:44:52,950 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.481秒 (要求: 1.500秒)
2025-07-31 01:44:52,950 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,950 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,951 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,951 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,951 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: gate+bybit SPK-USDT - 需要计算
2025-07-31 01:44:52,951 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: gate_bybit_SPK-USDT_50.0_50.0
2025-07-31 01:44:52,952 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] gate+bybit SPK-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,952 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: gate_bybit_SPK-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,952 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: gate+bybit SPK-USDT
2025-07-31 01:44:52,952 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,952 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,953 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,953 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,953 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: gate+okx SPK-USDT - 需要计算
2025-07-31 01:44:52,953 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: gate_okx_SPK-USDT_50.0_50.0
2025-07-31 01:44:52,954 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] gate+okx SPK-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,954 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: gate_okx_SPK-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,954 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: gate+okx SPK-USDT
2025-07-31 01:44:52,954 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,955 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,955 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,955 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,955 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: bybit+gate SPK-USDT - 需要计算
2025-07-31 01:44:52,956 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: bybit_gate_SPK-USDT_50.0_50.0
2025-07-31 01:44:52,956 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] bybit+gate SPK-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,956 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: bybit_gate_SPK-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,956 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: bybit+gate SPK-USDT
2025-07-31 01:44:52,957 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,957 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,957 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,958 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,958 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: bybit+okx SPK-USDT - 需要计算
2025-07-31 01:44:52,958 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: bybit_okx_SPK-USDT_50.0_50.0
2025-07-31 01:44:52,958 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] bybit+okx SPK-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,959 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: bybit_okx_SPK-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,959 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: bybit+okx SPK-USDT
2025-07-31 01:44:52,959 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,960 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,960 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,960 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,960 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: okx+gate SPK-USDT - 需要计算
2025-07-31 01:44:52,961 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: okx_gate_SPK-USDT_50.0_50.0
2025-07-31 01:44:52,961 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] okx+gate SPK-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,961 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: okx_gate_SPK-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,961 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: okx+gate SPK-USDT
2025-07-31 01:44:52,961 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx SPK-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,962 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,962 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit SPK-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,962 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,962 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: okx+bybit SPK-USDT - 需要计算
2025-07-31 01:44:52,963 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: okx_bybit_SPK-USDT_50.0_50.0
2025-07-31 01:44:52,963 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] okx+bybit SPK-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,963 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: okx_bybit_SPK-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,963 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: okx+bybit SPK-USDT
2025-07-31 01:44:52,963 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,964 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,964 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,964 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,965 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: gate+bybit RESOLV-USDT - 需要计算
2025-07-31 01:44:52,965 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: gate_bybit_RESOLV-USDT_50.0_50.0
2025-07-31 01:44:52,965 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] gate+bybit RESOLV-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,965 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: gate_bybit_RESOLV-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,966 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: gate+bybit RESOLV-USDT
2025-07-31 01:44:52,966 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,966 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,966 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,967 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,967 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: gate+okx RESOLV-USDT - 需要计算
2025-07-31 01:44:52,967 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: gate_okx_RESOLV-USDT_50.0_50.0
2025-07-31 01:44:52,967 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] gate+okx RESOLV-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,967 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: gate_okx_RESOLV-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,968 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: gate+okx RESOLV-USDT
2025-07-31 01:44:52,968 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,968 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,969 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,969 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,969 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: bybit+gate RESOLV-USDT - 需要计算
2025-07-31 01:44:52,969 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: bybit_gate_RESOLV-USDT_50.0_50.0
2025-07-31 01:44:52,970 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] bybit+gate RESOLV-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,970 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: bybit_gate_RESOLV-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,970 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: bybit+gate RESOLV-USDT
2025-07-31 01:44:52,970 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,970 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,971 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,971 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,971 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: bybit+okx RESOLV-USDT - 需要计算
2025-07-31 01:44:52,971 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: bybit_okx_RESOLV-USDT_50.0_50.0
2025-07-31 01:44:52,971 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] bybit+okx RESOLV-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,972 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: bybit_okx_RESOLV-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,972 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: bybit+okx RESOLV-USDT
2025-07-31 01:44:52,972 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,972 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,973 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,973 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,973 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: okx+gate RESOLV-USDT - 需要计算
2025-07-31 01:44:52,974 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: okx_gate_RESOLV-USDT_50.0_50.0
2025-07-31 01:44:52,974 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] okx+gate RESOLV-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,974 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: okx_gate_RESOLV-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,974 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: okx+gate RESOLV-USDT
2025-07-31 01:44:52,975 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx RESOLV-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,975 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,975 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit RESOLV-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,975 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,976 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: okx+bybit RESOLV-USDT - 需要计算
2025-07-31 01:44:52,976 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: okx_bybit_RESOLV-USDT_50.0_50.0
2025-07-31 01:44:52,976 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] okx+bybit RESOLV-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,976 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: okx_bybit_RESOLV-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,977 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: okx+bybit RESOLV-USDT
2025-07-31 01:44:52,977 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,977 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,977 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,977 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,978 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: gate+bybit ICNT-USDT - 需要计算
2025-07-31 01:44:52,978 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: gate_bybit_ICNT-USDT_50.0_50.0
2025-07-31 01:44:52,978 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] gate+bybit ICNT-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,978 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: gate_bybit_ICNT-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,979 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: gate+bybit ICNT-USDT
2025-07-31 01:44:52,979 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,979 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,980 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,980 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,980 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: gate+okx ICNT-USDT - 需要计算
2025-07-31 01:44:52,980 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: gate_okx_ICNT-USDT_50.0_50.0
2025-07-31 01:44:52,980 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] gate+okx ICNT-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,981 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: gate_okx_ICNT-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,981 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: gate+okx ICNT-USDT
2025-07-31 01:44:52,982 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,982 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,982 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,982 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,983 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: bybit+gate ICNT-USDT - 需要计算
2025-07-31 01:44:52,983 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: bybit_gate_ICNT-USDT_50.0_50.0
2025-07-31 01:44:52,983 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] bybit+gate ICNT-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,984 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: bybit_gate_ICNT-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,984 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: bybit+gate ICNT-USDT
2025-07-31 01:44:52,984 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,984 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,985 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,985 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,985 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: bybit+okx ICNT-USDT - 需要计算
2025-07-31 01:44:52,985 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: bybit_okx_ICNT-USDT_50.0_50.0
2025-07-31 01:44:52,986 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] bybit+okx ICNT-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,986 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: bybit_okx_ICNT-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,986 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: bybit+okx ICNT-USDT
2025-07-31 01:44:52,986 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,986 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,987 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,987 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,988 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: okx+gate ICNT-USDT - 需要计算
2025-07-31 01:44:52,988 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: okx_gate_ICNT-USDT_50.0_50.0
2025-07-31 01:44:52,988 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] okx+gate ICNT-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,988 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: okx_gate_ICNT-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,988 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: okx+gate ICNT-USDT
2025-07-31 01:44:52,989 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx ICNT-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,989 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,989 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit ICNT-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,989 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,990 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: okx+bybit ICNT-USDT - 需要计算
2025-07-31 01:44:52,990 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: okx_bybit_ICNT-USDT_50.0_50.0
2025-07-31 01:44:52,990 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] okx+bybit ICNT-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,991 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: okx_bybit_ICNT-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,991 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: okx+bybit ICNT-USDT
2025-07-31 01:44:52,991 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,991 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,991 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,992 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,992 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: gate+bybit CAKE-USDT - 需要计算
2025-07-31 01:44:52,992 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: gate_bybit_CAKE-USDT_50.0_50.0
2025-07-31 01:44:52,992 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] gate+bybit CAKE-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,992 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: gate_bybit_CAKE-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,993 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: gate+bybit CAKE-USDT
2025-07-31 01:44:52,993 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,993 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,993 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,994 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,994 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: gate+okx CAKE-USDT - 需要计算
2025-07-31 01:44:52,994 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: gate_okx_CAKE-USDT_50.0_50.0
2025-07-31 01:44:52,994 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] gate+okx CAKE-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,995 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: gate_okx_CAKE-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,995 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: gate+okx CAKE-USDT
2025-07-31 01:44:52,995 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,995 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,996 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,996 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:52,997 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: bybit+gate CAKE-USDT - 需要计算
2025-07-31 01:44:52,997 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: bybit_gate_CAKE-USDT_50.0_50.0
2025-07-31 01:44:52,997 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] bybit+gate CAKE-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,997 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: bybit_gate_CAKE-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:52,997 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: bybit+gate CAKE-USDT
2025-07-31 01:44:52,998 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,998 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:52,998 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:52,998 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:52,999 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: bybit+okx CAKE-USDT - 需要计算
2025-07-31 01:44:52,999 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: bybit_okx_CAKE-USDT_50.0_50.0
2025-07-31 01:44:52,999 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] bybit+okx CAKE-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:52,999 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: bybit_okx_CAKE-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:53,000 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: bybit+okx CAKE-USDT
2025-07-31 01:44:53,000 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:53,000 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:53,001 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: gate CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:53,001 [DEBUG] [core.trading_rules_preloader] 🔥 gate步长截取: 50.00000000 → 50.00000000 (步长=0.0001)
2025-07-31 01:44:53,001 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: okx+gate CAKE-USDT - 需要计算
2025-07-31 01:44:53,001 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: okx_gate_CAKE-USDT_50.0_50.0
2025-07-31 01:44:53,002 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] okx+gate CAKE-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:53,002 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: okx_gate_CAKE-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:53,002 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: okx+gate CAKE-USDT
2025-07-31 01:44:53,002 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: okx CAKE-USDT spot 步长=N/A 价格步长=0.01
2025-07-31 01:44:53,002 [DEBUG] [core.trading_rules_preloader] 🔥 okx步长截取: 50.00000000 → 50.00000000 (步长=0.00001)
2025-07-31 01:44:53,003 [INFO] [CacheMonitor] 📋 [交易规则缓存] 命中: bybit CAKE-USDT futures 步长=N/A 价格步长=0.01
2025-07-31 01:44:53,003 [DEBUG] [core.trading_rules_preloader] 🔥 bybit步长截取: 50.00000000 → 50.00000000 (步长=0.001)
2025-07-31 01:44:53,003 [INFO] [CacheMonitor] ⚖️ [对冲质量缓存] 未命中: okx+bybit CAKE-USDT - 需要计算
2025-07-31 01:44:53,003 [DEBUG] [core.trading_rules_preloader] 📊 对冲质量缓存未命中，开始计算: okx_bybit_CAKE-USDT_50.0_50.0
2025-07-31 01:44:53,004 [INFO] [CacheMonitor] ⚖️ [对冲质量计算] okx+bybit CAKE-USDT 现货=50.000000 期货=50.000000 比例=1.0000
2025-07-31 01:44:53,004 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量已缓存: okx_bybit_CAKE-USDT_50.0_50.0, 计算耗时: 0.00ms
2025-07-31 01:44:53,004 [DEBUG] [core.trading_rules_preloader] ✅ 对冲质量预热成功: okx+bybit CAKE-USDT
2025-07-31 01:44:53,005 [INFO] [core.trading_rules_preloader] ⚖️ 对冲质量缓存预热完成: 24个成功
2025-07-31 01:44:53,006 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '-0.009221', 'mark_price_round': '0.00001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '5000', 'interest_rate': '0.0003', 'index_price': '0.113026', 'order_price_round': '0.00001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'SPK_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.2', 'maintenance_rate': '0.008333', 'mark_type': 'index', 'funding_interval': 14400, 'type': 'direct', 'risk_limit_step': '9995000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '-0.009221', 'last_price': '0.11297', 'mark_price': '0.11278', 'order_size_max': 1000000, 'funding_next_apply': 1753905600, 'short_users': 691, 'config_change_time': 1753427642, 'create_time': 1750151108, 'trade_size': 200515291, 'position_size': 154121, 'long_users': 641, 'quanto_multiplier': '100', 'funding_impact_value': '5000', 'leverage_max': '75', 'cross_leverage_default': '10', 'risk_limit_max': '10000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 4254984, 'orderbook_id': 322396443, 'funding_cap_ratio': '4', 'voucher_leverage': '2', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1750151108}
2025-07-31 01:44:53,006 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: SPK-USDT -> 维持保证金率=0.833%
2025-07-31 01:44:53,007 [WARNING] [utils.margin_calculator] ⚠️ gate SPK-USDT quanto_multiplier异常大: 100.0
2025-07-31 01:44:53,007 [INFO] [utils.margin_calculator] ✅ gate SPK-USDT 合约信息验证通过
2025-07-31 01:44:53,007 [INFO] [utils.margin_calculator] ✅ [API调用] 保证金接口: gate_SPK-USDT | 合约信息获取成功并缓存
2025-07-31 01:44:53,007 [INFO] [utils.margin_calculator] 🔍 获取合约信息: bybit SPK-USDT (尝试 1/3)
2025-07-31 01:44:53,008 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: SPKUSDT
2025-07-31 01:44:53,008 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:44:53,008 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT
2025-07-31 01:44:53,008 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:44:53,009 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'SPKUSDT'}
2025-07-31 01:44:53,637 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: SPK-USDT -> 最大杠杆=25.0x
2025-07-31 01:44:53,638 [INFO] [utils.margin_calculator] ✅ bybit SPK-USDT 合约信息验证通过
2025-07-31 01:44:53,639 [INFO] [utils.margin_calculator] ✅ [API调用] 保证金接口: bybit_SPK-USDT | 合约信息获取成功并缓存
2025-07-31 01:44:53,639 [INFO] [utils.margin_calculator] 🔍 获取合约信息: okx SPK-USDT (尝试 1/3)
2025-07-31 01:44:53,640 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: SPK-USDT-SWAP
2025-07-31 01:44:53,640 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:44:53,641 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:58.020Z
2025-07-31 01:44:53,642 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'SPK-USDT-SWAP'}
2025-07-31 01:44:53,763 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'alias': '', 'auctionEndTime': '', 'baseCcy': '', 'category': '1', 'contTdSwTime': '', 'ctMult': '1', 'ctType': 'linear', 'ctVal': '100', 'ctValCcy': 'SPK', 'expTime': '', 'futureSettlement': False, 'instFamily': 'SPK-USDT', 'instId': 'SPK-USDT-SWAP', 'instIdCode': 214785, 'instType': 'SWAP', 'lever': '20', 'listTime': '1750156200346', 'lotSz': '1', 'maxIcebergSz': '100000000.0000000000000000', 'maxLmtAmt': '20000000', 'maxLmtSz': '100000000', 'maxMktAmt': '', 'maxMktSz': '20000', 'maxStopSz': '20000', 'maxTriggerSz': '100000000.0000000000000000', 'maxTwapSz': '100000000.0000000000000000', 'minSz': '1', 'openType': '', 'optType': '', 'quoteCcy': '', 'ruleType': 'normal', 'settleCcy': 'USDT', 'state': 'live', 'stk': '', 'tickSz': '0.00001', 'tradeQuoteCcyList': [], 'uly': 'SPK-USDT'}], 'msg': ''}
2025-07-31 01:44:53,764 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-31 01:44:53,765 [INFO] [utils.margin_calculator] ✅ okx SPK-USDT 合约信息验证通过
2025-07-31 01:44:53,765 [INFO] [utils.margin_calculator] ✅ [API调用] 保证金接口: okx_SPK-USDT | 合约信息获取成功并缓存
2025-07-31 01:44:53,765 [INFO] [utils.margin_calculator] 🔍 获取合约信息: gate RESOLV-USDT (尝试 1/3)
2025-07-31 01:44:53,766 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: RESOLV_USDT
2025-07-31 01:44:53,766 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 0.665秒 (要求: 1.500秒)
2025-07-31 01:44:54,421 [DEBUG] [exchanges.gate_exchange] Gate.io请求: POST https://api.gateio.ws/api/v4/futures/usdt/positions/SPK_USDT/leverage?leverage=3, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '4437db37bee3092912fe354de5b840da7feaf5501a2b54ecb58f9f10f61242a62afdefa718c509f784833feaa639b7416d5970ce00c6680dcccbe0f9a514e57f', 'Timestamp': '**********.4219182', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:54,423 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/spot/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '8f4b5938f292a93d383167d3d8a8322fc154062d833f21108053f78baa28c8cbdf6994e998347b437a5e1a6e433a552fec683d361c64e890f5ab1a1d58963eb7', 'Timestamp': '**********.4229496', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:54,424 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/RESOLV_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '22b0ac05eb0784a4abc33ade6b18ec2ed54df938d1c7e2af1660962d4594ff902a7cde92367d23fed14378e3001d0e5590a2d731e643ca42056ff36b5d208e1a', 'Timestamp': '**********.4247024', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:54,488 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'SPK_USDT', 'entry_price': '0', 'mark_price': '0.11288', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.01', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '50', 'history_pnl': '-0.3727315001', 'risk_limit': '5000', 'margin': '0', 'last_close_pnl': '-0.3390235001', 'liq_price': '0', 'update_time': 1753897498, 'update_id': 67, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:44:54,489 [INFO] [core.unified_leverage_manager] ✅ Gate.io杠杆设置成功: SPK-USDT 3倍
2025-07-31 01:44:54,700 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.224秒 (要求: 1.500秒)
2025-07-31 01:44:54,813 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.00005', 'mark_price_round': '0.0001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '5000', 'interest_rate': '0.0003', 'index_price': '0.19541', 'order_price_round': '0.0001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'RESOLV_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.2', 'maintenance_rate': '0.008333', 'mark_type': 'index', 'funding_interval': 14400, 'type': 'direct', 'risk_limit_step': '9995000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.00005', 'last_price': '0.1951', 'mark_price': '0.1953', 'order_size_max': 1000000, 'funding_next_apply': 1753905600, 'short_users': 113, 'config_change_time': 1753276879, 'create_time': 1749560719, 'trade_size': 320048467, 'position_size': 323319, 'long_users': 202, 'quanto_multiplier': '10', 'funding_impact_value': '5000', 'leverage_max': '75', 'cross_leverage_default': '10', 'risk_limit_max': '10000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 2155217, 'orderbook_id': 203804918, 'funding_cap_ratio': '4', 'voucher_leverage': '0', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1749560719}
2025-07-31 01:44:54,814 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: RESOLV-USDT -> 维持保证金率=0.833%
2025-07-31 01:44:54,814 [WARNING] [utils.margin_calculator] ⚠️ gate RESOLV-USDT quanto_multiplier异常大: 10.0
2025-07-31 01:44:54,815 [INFO] [utils.margin_calculator] ✅ gate RESOLV-USDT 合约信息验证通过
2025-07-31 01:44:54,815 [INFO] [utils.margin_calculator] ✅ [API调用] 保证金接口: gate_RESOLV-USDT | 合约信息获取成功并缓存
2025-07-31 01:44:54,816 [INFO] [utils.margin_calculator] 🔍 获取合约信息: bybit RESOLV-USDT (尝试 1/3)
2025-07-31 01:44:54,817 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: RESOLVUSDT
2025-07-31 01:44:54,817 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:44:54,818 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT
2025-07-31 01:44:54,818 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:44:54,819 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'RESOLVUSDT'}
2025-07-31 01:44:54,899 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): [{'currency': 'ICNT', 'available': '0.00425', 'locked': '0', 'update_id': 48}, {'currency': 'HUMA', 'available': '0.00087', 'locked': '0', 'update_id': 14}, {'currency': 'TMAI', 'available': '0.********', 'locked': '0', 'update_id': 53}, {'currency': 'DOGE', 'available': '0.00099', 'locked': '0', 'update_id': 4}, {'currency': 'USDT', 'available': '103.************', 'locked': '0', 'update_id': 1490}, {'currency': 'NEAR', 'available': '0.00799', 'locked': '0', 'update_id': 218}, {'currency': 'GT', 'available': '0.**********', 'locked': '0', 'update_id': 634}, {'currency': 'ALCH', 'available': '0.08', 'locked': '0', 'update_id': 26}, {'currency': 'SPK', 'available': '0.00943', 'locked': '0', 'update_id': 9}, {'currency': 'PEPE', 'available': '0.039', 'locked': '0', 'update_id': 6}, {'currency': '4EVER', 'available': '0.02703235', 'locked': '0', 'update_id': 26}, {'currency': 'GLS', 'available': '0.01086977', 'locked': '0', 'update_id': 219}, {'currency': 'BTC', 'available': '0.00000786', 'locked': '0', 'update_id': 93}, {'currency': 'UNI', 'available': '0.00952', 'locked': '0', 'update_id': 62}, {'currency': 'BNB', 'available': '0.000035', 'locked': '0', 'update_id': 20}, {'currency': 'ETH', 'available': '0.0000901', 'locked': '0', 'update_id': 6}, {'currency': 'RESOLV', 'available': '0.00609', 'locked': '0', 'update_id': 109}, {'currency': 'LINK', 'available': '0.00141', 'locked': '0', 'update_id': 12}, {'currency': 'PI', 'available': '0.00883', 'locked': '0', 'update_id': 4}, {'currency': 'LTC', 'available': '0.0000506', 'locked': '0', 'update_id': 25}, {'currency': 'POINT', 'available': '0.**********', 'locked': '0', 'update_id': 17}, {'currency': 'LAYER', 'available': '0', 'locked': '0', 'update_id': 0}, {'currency': 'ADA', 'available': '0.00216', 'locked': '0', 'update_id': 117}]
2025-07-31 01:44:54,900 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.025秒 (要求: 1.500秒)
2025-07-31 01:44:55,424 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: RESOLV-USDT -> 最大杠杆=25.0x
2025-07-31 01:44:55,425 [INFO] [utils.margin_calculator] ✅ bybit RESOLV-USDT 合约信息验证通过
2025-07-31 01:44:55,425 [INFO] [utils.margin_calculator] ✅ [API调用] 保证金接口: bybit_RESOLV-USDT | 合约信息获取成功并缓存
2025-07-31 01:44:55,426 [INFO] [utils.margin_calculator] 🔍 获取合约信息: okx RESOLV-USDT (尝试 1/3)
2025-07-31 01:44:55,426 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: RESOLV-USDT-SWAP
2025-07-31 01:44:55,427 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:44:55,427 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:44:59.806Z
2025-07-31 01:44:55,427 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'RESOLV-USDT-SWAP'}
2025-07-31 01:44:55,541 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'alias': '', 'auctionEndTime': '', 'baseCcy': '', 'category': '1', 'contTdSwTime': '', 'ctMult': '1', 'ctType': 'linear', 'ctVal': '10', 'ctValCcy': 'RESOLV', 'expTime': '', 'futureSettlement': False, 'instFamily': 'RESOLV-USDT', 'instId': 'RESOLV-USDT-SWAP', 'instIdCode': 213805, 'instType': 'SWAP', 'lever': '50', 'listTime': '1749565800438', 'lotSz': '1', 'maxIcebergSz': '100000000.0000000000000000', 'maxLmtAmt': '20000000', 'maxLmtSz': '100000000', 'maxMktAmt': '', 'maxMktSz': '50000', 'maxStopSz': '50000', 'maxTriggerSz': '100000000.0000000000000000', 'maxTwapSz': '100000000.0000000000000000', 'minSz': '1', 'openType': '', 'optType': '', 'quoteCcy': '', 'ruleType': 'normal', 'settleCcy': 'USDT', 'state': 'live', 'stk': '', 'tickSz': '0.0001', 'tradeQuoteCcyList': [], 'uly': 'RESOLV-USDT'}], 'msg': ''}
2025-07-31 01:44:55,542 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 01:44:55,543 [INFO] [utils.margin_calculator] ✅ okx RESOLV-USDT 合约信息验证通过
2025-07-31 01:44:55,543 [INFO] [utils.margin_calculator] ✅ [API调用] 保证金接口: okx_RESOLV-USDT | 合约信息获取成功并缓存
2025-07-31 01:44:55,544 [INFO] [utils.margin_calculator] 🔍 获取合约信息: gate ICNT-USDT (尝试 1/3)
2025-07-31 01:44:55,544 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: ICNT_USDT
2025-07-31 01:44:55,544 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 0.380秒 (要求: 1.500秒)
2025-07-31 01:44:55,920 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'a798b522d76e7544155515f467cb6ed6025ada3bb8aed974cad773c69038995ccfd903c12e9733ae2a33c674ae50b93c3196499e71c68981c90566978a2b5680', 'Timestamp': '**********.9208562', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:55,922 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/ICNT_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '59d3f0d129ae55423020300ca2187e98efec3f8a50f1209170e5d94ffa8caf6a2ad1bcb9cc36a2050db56fe4298e508bccc18b4061652c846347b6e995890cce', 'Timestamp': '**********.9228368', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:55,924 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/positions/SPK_USDT, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'c68bd4f1a3dba3a3b870cf36e4bbf920ff96c5e37666a585442df8b8fab54d65eced511c5dc967ae941a5cb3b50cda2ff26d7db4dc1fc3001a9c4bd5d41e15ba', 'Timestamp': '**********.9244902', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:55,978 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.00005', 'mark_price_round': '0.0001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '5000', 'interest_rate': '0.0003', 'index_price': '0.21556', 'order_price_round': '0.0001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'ICNT_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.2', 'maintenance_rate': '0.01', 'mark_type': 'index', 'funding_interval': 14400, 'type': 'direct', 'risk_limit_step': '2995000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.00005', 'last_price': '0.2159', 'mark_price': '0.2158', 'order_size_max': 1000000, 'funding_next_apply': 1753905600, 'short_users': 207, 'config_change_time': 1753883657, 'create_time': 1751510739, 'trade_size': 62064535, 'position_size': 179771, 'long_users': 192, 'quanto_multiplier': '10', 'funding_impact_value': '5000', 'leverage_max': '50', 'cross_leverage_default': '10', 'risk_limit_max': '3000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 1085025, 'orderbook_id': 91767855, 'funding_cap_ratio': '2', 'voucher_leverage': '0', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1751547600}
2025-07-31 01:44:55,980 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: ICNT-USDT -> 维持保证金率=1.000%
2025-07-31 01:44:55,980 [WARNING] [utils.margin_calculator] ⚠️ gate ICNT-USDT quanto_multiplier异常大: 10.0
2025-07-31 01:44:55,981 [INFO] [utils.margin_calculator] ✅ gate ICNT-USDT 合约信息验证通过
2025-07-31 01:44:55,981 [INFO] [utils.margin_calculator] ✅ [API调用] 保证金接口: gate_ICNT-USDT | 合约信息获取成功并缓存
2025-07-31 01:44:55,982 [INFO] [utils.margin_calculator] 🔍 获取合约信息: bybit ICNT-USDT (尝试 1/3)
2025-07-31 01:44:55,982 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: ICNTUSDT
2025-07-31 01:44:55,983 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 0.334秒 (要求: 1.500秒)
2025-07-31 01:44:55,984 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'SPK_USDT', 'entry_price': '0', 'mark_price': '0.11288', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.01', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '50', 'history_pnl': '-0.3727315001', 'risk_limit': '5000', 'margin': '0', 'last_close_pnl': '-0.3390235001', 'liq_price': '0', 'update_time': 1753897498, 'update_id': 67, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:44:55,985 [INFO] [core.unified_leverage_manager] 🔍 Gate.io杠杆验证: 设置3倍, 实际3倍
2025-07-31 01:44:55,986 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: gate SPK-USDT -> 3x
2025-07-31 01:44:55,987 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: gate SPK-USDT 3x
2025-07-31 01:44:55,987 [INFO] [core.unified_leverage_manager] 🔧 Gate统一杠杆设置: RESOLV-USDT 3倍
2025-07-31 01:44:55,988 [INFO] [core.unified_leverage_manager] 🔧 Gate.io杠杆设置: 合约=RESOLV_USDT, 杠杆=3倍
2025-07-31 01:44:55,988 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.435秒 (要求: 1.500秒)
2025-07-31 01:44:55,989 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'order_margin': '0', 'point': '0', 'bonus': '0', 'history': {'dnw': '-124.********', 'pnl': '262.***********', 'refr': '0', 'point_fee': '0', 'fund': '1.***********', 'bonus_dnw': '0', 'point_refr': '0', 'bonus_offset': '0', 'fee': '-34.********', 'point_dnw': '0', 'cross_settle': '0'}, 'unrealised_pnl': '0', 'total': '104.***********', 'available': '104.***********', 'enable_credit': False, 'in_dual_mode': False, 'currency': 'USDT', 'position_margin': '0', 'user': ********, 'update_time': **********, 'update_id': 1158, 'position_initial_margin': '0', 'maintenance_margin': '0', 'margin_mode': 0, 'enable_evolved_classic': True, 'cross_initial_margin': '0', 'cross_maintenance_margin': '0', 'cross_order_margin': '0', 'cross_unrealised_pnl': '0', 'cross_virtual_unrealised_pnl': '0', 'cross_available': '104.***********', 'isolated_position_margin': '0', 'enable_new_dual_mode': False, 'margin_mode_name': 'classic', 'cross_margin_balance': '104.***********', 'cross_mmr': '0', 'cross_imr': '0', 'enable_tiered_mm': False, 'position_voucher_total': '0'}
2025-07-31 01:44:55,991 [DEBUG] [UnifiedBalanceManager] gate 分离账户: 现货$103.19, 期货$104.64
2025-07-31 01:44:55,991 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-31 01:44:55,992 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 0.325秒 (要求: 1.500秒)
2025-07-31 01:44:56,325 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 1 → 1 参数
2025-07-31 01:44:56,327 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:44:56,328 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT
2025-07-31 01:44:56,328 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:44:56,329 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'ICNTUSDT'}
2025-07-31 01:44:56,928 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: **********155388902 -> **********155ms
2025-07-31 01:44:56,929 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: *************, 服务器时间: **********155, 偏移: 4226ms
2025-07-31 01:44:56,930 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897496930, 偏移=4226, 调整后=**********156
2025-07-31 01:44:56,930 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: **********156
2025-07-31 01:44:56,931 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:44:56,931 [DEBUG] [exchanges.bybit_exchange]   timestamp: **********156
2025-07-31 01:44:56,932 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:44:56,933 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:44:56,933 [DEBUG] [exchanges.bybit_exchange]   param_str: accountType=UNIFIED
2025-07-31 01:44:56,933 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 54
2025-07-31 01:44:56,934 [DEBUG] [exchanges.bybit_exchange]   生成的签名: f3cccb2ec139ca60...
2025-07-31 01:44:56,934 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED
2025-07-31 01:44:56,935 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '**********156', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': 'f3cccb2ec139ca60a7424c0cc0227754d4dda9ec8e5072e8909ca4f9a29c3fbb'}
2025-07-31 01:44:56,936 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'accountType': 'UNIFIED'}
2025-07-31 01:44:57,042 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: ICNT-USDT -> 最大杠杆=20.0x
2025-07-31 01:44:57,043 [INFO] [utils.margin_calculator] ✅ bybit ICNT-USDT 合约信息验证通过
2025-07-31 01:44:57,044 [INFO] [utils.margin_calculator] ✅ [API调用] 保证金接口: bybit_ICNT-USDT | 合约信息获取成功并缓存
2025-07-31 01:44:57,044 [INFO] [utils.margin_calculator] 🔍 获取合约信息: okx ICNT-USDT (尝试 1/3)
2025-07-31 01:44:57,045 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: ICNT-USDT-SWAP
2025-07-31 01:44:57,046 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:44:57,046 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:01.426Z
2025-07-31 01:44:57,046 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'ICNT-USDT-SWAP'}
2025-07-31 01:44:57,166 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:44:57,167 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:44:57,168 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:44:57,168 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:44:57,169 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/public/instruments
2025-07-31 01:44:57,169 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:44:57,170 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 01:44:57,170 [WARNING] [utils.margin_calculator] ⚠️ 合约信息无效: okx ICNT-USDT (尝试 1)
2025-07-31 01:44:57,238 [DEBUG] [exchanges.bybit_exchange] Bybit原始余额响应: {'list': [{'accountIMRate': 0.0, 'totalMaintenanceMarginByMp': '0', 'totalInitialMargin': '0', 'accountType': 'UNIFIED', 'accountMMRate': 0.0, 'accountMMRateByMp': 0.0, 'accountIMRateByMp': 0.0, 'totalInitialMarginByMp': '0', 'totalMaintenanceMargin': '0', 'totalEquity': '525.6897954', 'totalMarginBalance': 521.********, 'totalAvailableBalance': 521.********, 'totalPerpUPL': '0', 'totalWalletBalance': 521.********, 'accountLTV': '0', 'coin': [{'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0.00984', 'totalPositionMM': '0', 'usdValue': 3.********, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.00984, 'cumRealisedPnl': '-0.00016', 'locked': '0', 'marginCollateral': False, 'coin': 'COINX'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0', 'totalPositionMM': '0', 'usdValue': 0.********, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.0, 'cumRealisedPnl': '-0.********', 'locked': '0', 'marginCollateral': True, 'coin': 'BTC'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '520.********', 'totalPositionMM': '0', 'usdValue': 521.********, 'unrealisedPnl': '0', 'collateralSwitch': True, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 520.********, 'cumRealisedPnl': '-47.09688583', 'locked': '0', 'marginCollateral': True, 'coin': 'USDT'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '1.13228612', 'totalPositionMM': '0', 'usdValue': 0.85731609, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 1.13228612, 'cumRealisedPnl': '0', 'locked': '0', 'marginCollateral': True, 'coin': 'MNT'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0.0324', 'totalPositionMM': '0', 'usdValue': 0.00366395, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.0324, 'cumRealisedPnl': '-4.0635', 'locked': '0', 'marginCollateral': True, 'coin': 'SPK'}]}]}
2025-07-31 01:44:57,240 [INFO] [exchanges.bybit_exchange] Bybit UNIFIED账户总可用余额: $521.05 USD
2025-07-31 01:44:57,240 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED COINX: 钱包=0.009840, 锁定=0.000000, 可用=0.009840
2025-07-31 01:44:57,241 [DEBUG] [exchanges.bybit_exchange] Bybit COINX 最终余额: 可用=0.009840, 锁定=0.000000
2025-07-31 01:44:57,241 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED BTC: 钱包=0.000000, 锁定=0.000000, 可用=0.000000
2025-07-31 01:44:57,242 [INFO] [exchanges.bybit_exchange] Bybit USDT余额详细计算: 钱包余额=520.99, 锁定=0.00, 订单保证金=0.00, 持仓保证金=0.00, 可用余额=520.99
2025-07-31 01:44:57,243 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED USDT: 钱包=520.986453, 锁定=0.000000, 可用=520.986453
2025-07-31 01:44:57,243 [DEBUG] [exchanges.bybit_exchange] Bybit USDT 最终余额: 可用=520.986453, 锁定=0.000000
2025-07-31 01:44:57,244 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED MNT: 钱包=1.132286, 锁定=0.000000, 可用=1.132286
2025-07-31 01:44:57,244 [DEBUG] [exchanges.bybit_exchange] Bybit MNT 最终余额: 可用=1.132286, 锁定=0.000000
2025-07-31 01:44:57,244 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED SPK: 钱包=0.032400, 锁定=0.000000, 可用=0.032400
2025-07-31 01:44:57,245 [DEBUG] [exchanges.bybit_exchange] Bybit SPK 最终余额: 可用=0.032400, 锁定=0.000000
2025-07-31 01:44:57,246 [INFO] [exchanges.bybit_exchange] Bybit账户余额处理完成: 4个币种
2025-07-31 01:44:57,246 [DEBUG] [UnifiedBalanceManager] bybit 统一账户: $520.99
2025-07-31 01:44:57,246 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.300秒 (要求: 0.500秒)
2025-07-31 01:44:57,428 [DEBUG] [exchanges.gate_exchange] Gate.io请求: POST https://api.gateio.ws/api/v4/futures/usdt/positions/RESOLV_USDT/leverage?leverage=3, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'd99e4242be9e288479d388191840cf816b42718a77d6b34757a0821994a51c0274c7dc13832b62f50d9d96388e1d78d4958508384b7d996b1cdc88a5194af3ff', 'Timestamp': '1753897497.428231', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:57,489 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'RESOLV_USDT', 'entry_price': '0', 'mark_price': '0.1953', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.008333', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '75', 'history_pnl': '-1.416401', 'risk_limit': '5000', 'margin': '0', 'last_close_pnl': '0.3672065', 'liq_price': '0', 'update_time': **********, 'update_id': 86, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:44:57,489 [INFO] [core.unified_leverage_manager] ✅ Gate.io杠杆设置成功: RESOLV-USDT 3倍
2025-07-31 01:44:57,552 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/balance
2025-07-31 01:44:57,552 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:01.931Z
2025-07-31 01:44:57,553 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:44:57,665 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'adjEq': '', 'availEq': '', 'borrowFroz': '', 'details': [{'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '221.*************', 'availEq': '221.*************', 'borrowFroz': '', 'cashBal': '221.*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '221.*************', 'eqUsd': '221.*************', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1753891303843', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '0.2844993821824943', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.0441', 'availEq': '0.0441', 'borrowFroz': '', 'cashBal': '0.0441', 'ccy': 'SPK', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.0441', 'eqUsd': '0.00498771', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '0.098321391753442', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '0.0441', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '0.0006517366236732', 'spotUplRatio': '0.1503091848375984', 'stgyEq': '0', 'totalPnl': '-0.0075587127542462', 'totalPnlRatio': '-0.6024595936468821', 'twap': '0', 'uTime': '1753860251957', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.00000000289', 'availEq': '0.00000000289', 'borrowFroz': '', 'cashBal': '0.00000000289', 'ccy': 'BTC', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.00000000289', 'eqUsd': '0.000340520608', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748504762280', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000746', 'availEq': '0.000000746', 'borrowFroz': '', 'cashBal': '0.000000746', 'ccy': 'SOL', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000746', 'eqUsd': '0.00013369812', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748504762277', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.0000697', 'availEq': '0.0000697', 'borrowFroz': '', 'cashBal': '0.0000697', 'ccy': 'ADA', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.0000697', 'eqUsd': '0.00005405235', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1750852693782', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000567', 'availEq': '0.000000567', 'borrowFroz': '', 'cashBal': '0.000000567', 'ccy': 'UNI', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000567', 'eqUsd': '0.000005778297', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1750074898770', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000099', 'availEq': '0.000000099', 'borrowFroz': '', 'cashBal': '0.000000099', 'ccy': 'FIL', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000099', 'eqUsd': '0.000000249777', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1752849679083', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000004', 'availEq': '0.000000004', 'borrowFroz': '', 'cashBal': '0.000000004', 'ccy': 'LINK', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000004', 'eqUsd': '0.00000007084', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748614312217', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000004', 'availEq': '0.000000004', 'borrowFroz': '', 'cashBal': '0.000000004', 'ccy': 'ATOM', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000004', 'eqUsd': '0.000000017756', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748626688438', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.00000000455', 'availEq': '0.00000000455', 'borrowFroz': '', 'cashBal': '0.00000000455', 'ccy': 'NEAR', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.00000000455', 'eqUsd': '0.00000001216215', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1750157106950', 'upl': '0', 'uplLiab': ''}], 'imr': '', 'isoEq': '0', 'mgnRatio': '', 'mmr': '', 'notionalUsd': '', 'notionalUsdForBorrow': '', 'notionalUsdForFutures': '', 'notionalUsdForOption': '', 'notionalUsdForSwap': '', 'ordFroz': '', 'totalEq': '221.557480293366', 'uTime': '**********987', 'upl': ''}], 'msg': ''}
2025-07-31 01:44:57,669 [DEBUG] [exchanges.okx_exchange] OKX使用统一账户API获取余额成功
2025-07-31 01:44:57,670 [DEBUG] [exchanges.okx_exchange] OKX检测到统一账户API响应格式
2025-07-31 01:44:57,670 [DEBUG] [exchanges.okx_exchange] OKX统一账户 USDT 余额: 可用=221.*************, 冻结=0.0
2025-07-31 01:44:57,671 [DEBUG] [exchanges.okx_exchange] OKX统一账户 SPK 余额: 可用=0.0441, 冻结=0.0
2025-07-31 01:44:57,671 [DEBUG] [exchanges.okx_exchange] OKX统一账户 BTC 余额: 可用=2.89e-09, 冻结=0.0
2025-07-31 01:44:57,671 [DEBUG] [exchanges.okx_exchange] OKX统一账户 SOL 余额: 可用=7.46e-07, 冻结=0.0
2025-07-31 01:44:57,672 [DEBUG] [exchanges.okx_exchange] OKX统一账户 ADA 余额: 可用=6.97e-05, 冻结=0.0
2025-07-31 01:44:57,673 [DEBUG] [exchanges.okx_exchange] OKX统一账户 UNI 余额: 可用=5.67e-07, 冻结=0.0
2025-07-31 01:44:57,673 [DEBUG] [exchanges.okx_exchange] OKX统一账户 FIL 余额: 可用=9.9e-08, 冻结=0.0
2025-07-31 01:44:57,674 [DEBUG] [exchanges.okx_exchange] OKX统一账户 LINK 余额: 可用=4e-09, 冻结=0.0
2025-07-31 01:44:57,674 [DEBUG] [exchanges.okx_exchange] OKX统一账户 ATOM 余额: 可用=4e-09, 冻结=0.0
2025-07-31 01:44:57,675 [DEBUG] [exchanges.okx_exchange] OKX统一账户 NEAR 余额: 可用=4.55e-09, 冻结=0.0
2025-07-31 01:44:57,675 [DEBUG] [exchanges.okx_exchange] OKX余额解析完成: {'USDT': {'available': 221.*************, 'locked': 0.0}, 'SPK': {'available': 0.0441, 'locked': 0.0}, 'BTC': {'available': 2.89e-09, 'locked': 0.0}, 'SOL': {'available': 7.46e-07, 'locked': 0.0}, 'ADA': {'available': 6.97e-05, 'locked': 0.0}, 'UNI': {'available': 5.67e-07, 'locked': 0.0}, 'FIL': {'available': 9.9e-08, 'locked': 0.0}, 'LINK': {'available': 4e-09, 'locked': 0.0}, 'ATOM': {'available': 4e-09, 'locked': 0.0}, 'NEAR': {'available': 4.55e-09, 'locked': 0.0}}
2025-07-31 01:44:57,676 [DEBUG] [UnifiedBalanceManager] okx 统一账户: $221.55
2025-07-31 01:44:57,676 [INFO] [CacheMonitor] 💰 [余额缓存] 更新: system gate_spot_usdt $0.00 -> $103.19 (+$103.19)
2025-07-31 01:44:57,677 [INFO] [CacheMonitor] 💰 [余额缓存] 更新: system gate_futures_usdt $0.00 -> $104.64 (+$104.64)
2025-07-31 01:44:57,677 [INFO] [CacheMonitor] 💰 [余额缓存] 更新: system bybit_unified_usdt $0.00 -> $520.99 (+$520.99)
2025-07-31 01:44:57,678 [INFO] [CacheMonitor] 💰 [余额缓存] 更新: system okx_unified_usdt $0.00 -> $221.55 (+$221.55)
2025-07-31 01:44:57,678 [INFO] [UnifiedBalanceManager] ✅ 余额缓存更新完成: 4个账户
2025-07-31 01:44:57,679 [INFO] [core.trading_rules_preloader] 💳 余额缓存预热完成: 4个账户
2025-07-31 01:44:57,679 [INFO] [utils.margin_calculator] 🔍 获取合约信息: okx ICNT-USDT (尝试 2/3)
2025-07-31 01:44:57,680 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: ICNT-USDT-SWAP
2025-07-31 01:44:57,680 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.371秒 (要求: 0.500秒)
2025-07-31 01:44:57,681 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.247秒 (要求: 1.500秒)
2025-07-31 01:44:58,060 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:44:58,060 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:02.440Z
2025-07-31 01:44:58,061 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'ICNT-USDT-SWAP'}
2025-07-31 01:44:58,170 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:44:58,171 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:44:58,171 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:44:58,172 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:44:58,172 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/public/instruments
2025-07-31 01:44:58,173 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:44:58,174 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 01:44:58,174 [WARNING] [utils.margin_calculator] ⚠️ 合约信息无效: okx ICNT-USDT (尝试 2)
2025-07-31 01:44:58,683 [INFO] [utils.margin_calculator] 🔍 获取合约信息: okx ICNT-USDT (尝试 3/3)
2025-07-31 01:44:58,683 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: ICNT-USDT-SWAP
2025-07-31 01:44:58,684 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:44:58,685 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:03.064Z
2025-07-31 01:44:58,685 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'ICNT-USDT-SWAP'}
2025-07-31 01:44:58,796 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:44:58,797 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:44:58,797 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:44:58,798 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:44:58,798 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/public/instruments
2025-07-31 01:44:58,799 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:44:58,800 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 01:44:58,800 [WARNING] [utils.margin_calculator] ⚠️ 合约信息无效: okx ICNT-USDT (尝试 3)
2025-07-31 01:44:58,801 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-07-31 01:44:58,801 [INFO] [utils.margin_calculator] ✅ 生成默认合约信息: okx ICNT-USDT
2025-07-31 01:44:58,801 [WARNING] [utils.margin_calculator] 🔧 使用默认合约信息作为回退: okx ICNT-USDT
2025-07-31 01:44:58,802 [INFO] [utils.margin_calculator] 🔍 获取合约信息: gate CAKE-USDT (尝试 1/3)
2025-07-31 01:44:58,803 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: CAKE_USDT
2025-07-31 01:44:58,803 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 0.125秒 (要求: 1.500秒)
2025-07-31 01:44:58,931 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/positions/RESOLV_USDT, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'a942e5c58542142754cb0d45fa72ed6756361a71c1f9451ef60d4aa28245d9e697cc2df81b0bea20abd7ca9affe8f74e3656678041ab6634ca99e7881e546e66', 'Timestamp': '1753897498.9316003', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:58,933 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/CAKE_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '96b1cc36459119e3129d56efaa4373b255391a489707281e6bdca20eba13c740e479731c5c37f4662c3e881fb097ad34c19460fc51c5293764330b88718c0d95', 'Timestamp': '1753897498.9334116', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:44:58,991 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.00005', 'mark_price_round': '0.0001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '5000', 'interest_rate': '0.0003', 'index_price': '2.808', 'order_price_round': '0.0001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'CAKE_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.15', 'maintenance_rate': '0.006', 'mark_type': 'index', 'funding_interval': 14400, 'type': 'direct', 'risk_limit_step': '4995000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.00005', 'last_price': '2.8092', 'mark_price': '2.808', 'order_size_max': 10000000, 'funding_next_apply': 1753905600, 'short_users': 176, 'config_change_time': 1753344251, 'create_time': 1615334400, 'trade_size': 2862176276, 'position_size': 4227262, 'long_users': 276, 'quanto_multiplier': '0.1', 'funding_impact_value': '5000', 'leverage_max': '75', 'cross_leverage_default': '10', 'risk_limit_max': '5000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 6572593, 'orderbook_id': 1269754441, 'funding_cap_ratio': '2', 'voucher_leverage': '0', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1615334400}
2025-07-31 01:44:58,992 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: CAKE-USDT -> 维持保证金率=0.600%
2025-07-31 01:44:58,992 [INFO] [utils.margin_calculator] ✅ gate CAKE-USDT 合约信息验证通过
2025-07-31 01:44:58,993 [INFO] [utils.margin_calculator] ✅ [API调用] 保证金接口: gate_CAKE-USDT | 合约信息获取成功并缓存
2025-07-31 01:44:58,994 [INFO] [utils.margin_calculator] 🔍 获取合约信息: bybit CAKE-USDT (尝试 1/3)
2025-07-31 01:44:58,994 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: CAKEUSDT
2025-07-31 01:44:58,995 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:44:58,995 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT
2025-07-31 01:44:58,995 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:44:58,996 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'CAKEUSDT'}
2025-07-31 01:44:58,998 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'RESOLV_USDT', 'entry_price': '0', 'mark_price': '0.1953', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.008333', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '75', 'history_pnl': '-1.416401', 'risk_limit': '5000', 'margin': '0', 'last_close_pnl': '0.3672065', 'liq_price': '0', 'update_time': **********, 'update_id': 86, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:44:58,999 [INFO] [core.unified_leverage_manager] 🔍 Gate.io杠杆验证: 设置3倍, 实际3倍
2025-07-31 01:44:59,000 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: gate RESOLV-USDT -> 3x
2025-07-31 01:44:59,000 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: gate RESOLV-USDT 3x
2025-07-31 01:44:59,001 [INFO] [core.unified_leverage_manager] 🔧 Gate统一杠杆设置: ICNT-USDT 3倍
2025-07-31 01:44:59,001 [INFO] [core.unified_leverage_manager] 🔧 Gate.io杠杆设置: 合约=ICNT_USDT, 杠杆=3倍
2025-07-31 01:44:59,002 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.431秒 (要求: 1.500秒)
2025-07-31 01:44:59,603 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: CAKE-USDT -> 最大杠杆=25.0x
2025-07-31 01:44:59,604 [INFO] [utils.margin_calculator] ✅ bybit CAKE-USDT 合约信息验证通过
2025-07-31 01:44:59,604 [INFO] [utils.margin_calculator] ✅ [API调用] 保证金接口: bybit_CAKE-USDT | 合约信息获取成功并缓存
2025-07-31 01:44:59,605 [INFO] [utils.margin_calculator] 🔍 获取合约信息: okx CAKE-USDT (尝试 1/3)
2025-07-31 01:44:59,605 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: CAKE-USDT-SWAP
2025-07-31 01:44:59,606 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:44:59,607 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:03.986Z
2025-07-31 01:44:59,607 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'CAKE-USDT-SWAP'}
2025-07-31 01:44:59,731 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:44:59,732 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:44:59,732 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:44:59,733 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:44:59,733 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/public/instruments
2025-07-31 01:44:59,734 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:44:59,734 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 01:44:59,735 [WARNING] [utils.margin_calculator] ⚠️ 合约信息无效: okx CAKE-USDT (尝试 1)
2025-07-31 01:45:00,243 [INFO] [utils.margin_calculator] 🔍 获取合约信息: okx CAKE-USDT (尝试 2/3)
2025-07-31 01:45:00,243 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: CAKE-USDT-SWAP
2025-07-31 01:45:00,244 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:45:00,245 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:04.624Z
2025-07-31 01:45:00,245 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'CAKE-USDT-SWAP'}
2025-07-31 01:45:00,353 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:45:00,353 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:45:00,354 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:45:00,354 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:45:00,355 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/public/instruments
2025-07-31 01:45:00,355 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:45:00,355 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 01:45:00,356 [WARNING] [utils.margin_calculator] ⚠️ 合约信息无效: okx CAKE-USDT (尝试 2)
2025-07-31 01:45:00,447 [DEBUG] [exchanges.gate_exchange] Gate.io请求: POST https://api.gateio.ws/api/v4/futures/usdt/positions/ICNT_USDT/leverage?leverage=3, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'a755bebd23a95d5473c2d95028213e86132e477003a56efb9b21b4d081ccc8c6411e2fcda4781d714c2318c924858f671d0c5d51bb9db16a43a65afdbe43161e', 'Timestamp': '1753897500.447498', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:00,508 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'ICNT_USDT', 'entry_price': '0', 'mark_price': '0.2158', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.01', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '50', 'history_pnl': '-3.99498960791', 'risk_limit': '5000', 'margin': '0', 'last_close_pnl': '1.350251', 'liq_price': '0', 'update_time': 1753897504, 'update_id': 121, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:45:00,509 [INFO] [core.unified_leverage_manager] ✅ Gate.io杠杆设置成功: ICNT-USDT 3倍
2025-07-31 01:45:00,711 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.236秒 (要求: 1.500秒)
2025-07-31 01:45:00,851 [INFO] [utils.margin_calculator] 🔍 获取合约信息: okx CAKE-USDT (尝试 3/3)
2025-07-31 01:45:00,851 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: CAKE-USDT-SWAP
2025-07-31 01:45:00,852 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:45:00,853 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:05.232Z
2025-07-31 01:45:00,853 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'CAKE-USDT-SWAP'}
2025-07-31 01:45:00,961 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:45:00,962 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:45:00,962 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:45:00,962 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:45:00,963 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/public/instruments
2025-07-31 01:45:00,963 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:45:00,963 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 01:45:00,963 [WARNING] [utils.margin_calculator] ⚠️ 合约信息无效: okx CAKE-USDT (尝试 3)
2025-07-31 01:45:00,964 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-07-31 01:45:00,964 [INFO] [utils.margin_calculator] ✅ 生成默认合约信息: okx CAKE-USDT
2025-07-31 01:45:00,965 [WARNING] [utils.margin_calculator] 🔧 使用默认合约信息作为回退: okx CAKE-USDT
2025-07-31 01:45:00,965 [INFO] [core.trading_rules_preloader] 💰 保证金缓存预热完成: 12个成功
2025-07-31 01:45:01,955 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/positions/ICNT_USDT, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'dec0048c98d9fb3a6a70ac92a7676a019d49b818feb752ff5caebcdd6546982ce42207cf3408eb8ec544bdaca0c0fa26f9410f12d42bcb6fcc06c6ca9b2ba24c', 'Timestamp': '**********.955375', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:02,019 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'ICNT_USDT', 'entry_price': '0', 'mark_price': '0.2158', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.01', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '50', 'history_pnl': '-3.99498960791', 'risk_limit': '5000', 'margin': '0', 'last_close_pnl': '1.350251', 'liq_price': '0', 'update_time': 1753897504, 'update_id': 121, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:45:02,019 [INFO] [core.unified_leverage_manager] 🔍 Gate.io杠杆验证: 设置3倍, 实际3倍
2025-07-31 01:45:02,020 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: gate ICNT-USDT -> 3x
2025-07-31 01:45:02,020 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: gate ICNT-USDT 3x
2025-07-31 01:45:02,021 [INFO] [core.unified_leverage_manager] 🔧 Gate统一杠杆设置: CAKE-USDT 3倍
2025-07-31 01:45:02,022 [INFO] [core.unified_leverage_manager] 🔧 Gate.io杠杆设置: 合约=CAKE_USDT, 杠杆=3倍
2025-07-31 01:45:02,022 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.433秒 (要求: 1.500秒)
2025-07-31 01:45:03,452 [DEBUG] [exchanges.gate_exchange] Gate.io请求: POST https://api.gateio.ws/api/v4/futures/usdt/positions/CAKE_USDT/leverage?leverage=3, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'df4c8b3e9f79c60d5caf3086a42adcc26a1a9e758ecd78fe2cce6658793663d72c21bb3a2f51df8aef8c91aff31f72882cf37b361d9a893509b767a206a8ad91', 'Timestamp': '1753897503.45297', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:03,521 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'CAKE_USDT', 'entry_price': '0', 'mark_price': '2.8079', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.006', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '75', 'history_pnl': '0', 'risk_limit': '5000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897507, 'update_id': 44, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:45:03,521 [INFO] [core.unified_leverage_manager] ✅ Gate.io杠杆设置成功: CAKE-USDT 3倍
2025-07-31 01:45:03,735 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.217秒 (要求: 1.500秒)
2025-07-31 01:45:04,966 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/positions/CAKE_USDT, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'a2f879f0b248c17bda96dc21e26cd4e751852e67526d0acb0f40c091a523f94f13fc65d6a6215ed7436c33f22857035adbd7353e40d319bfa84a7c7517a2f13d', 'Timestamp': '1753897504.9660673', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:05,028 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'CAKE_USDT', 'entry_price': '0', 'mark_price': '2.8073', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.006', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '75', 'history_pnl': '0', 'risk_limit': '5000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897507, 'update_id': 44, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:45:05,029 [INFO] [core.unified_leverage_manager] 🔍 Gate.io杠杆验证: 设置3倍, 实际3倍
2025-07-31 01:45:05,029 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: gate CAKE-USDT -> 3x
2025-07-31 01:45:05,030 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: gate CAKE-USDT 3x
2025-07-31 01:45:05,031 [INFO] [core.unified_leverage_manager] 🔧 Gate统一杠杆设置: WIF-USDT 3倍
2025-07-31 01:45:05,031 [INFO] [core.unified_leverage_manager] 🔧 Gate.io杠杆设置: 合约=WIF_USDT, 杠杆=3倍
2025-07-31 01:45:05,032 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.434秒 (要求: 1.500秒)
2025-07-31 01:45:06,482 [DEBUG] [exchanges.gate_exchange] Gate.io请求: POST https://api.gateio.ws/api/v4/futures/usdt/positions/WIF_USDT/leverage?leverage=3, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '48f38b34fbe3371aae4d94da4a1b1a28e867314975e5317d4f392644c7429b777efe4dbfe8f4f334703dfd4a06d19cd4fbe7899f169fc2c8cf5673bc8d11fd0b', 'Timestamp': '1753897506.4827826', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:06,543 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'WIF_USDT', 'entry_price': '0', 'mark_price': '0.9824', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.01', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '50', 'history_pnl': '0', 'risk_limit': '20000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897510, 'update_id': 43, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:45:06,544 [INFO] [core.unified_leverage_manager] ✅ Gate.io杠杆设置成功: WIF-USDT 3倍
2025-07-31 01:45:06,730 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.252秒 (要求: 1.500秒)
2025-07-31 01:45:07,983 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/positions/WIF_USDT, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '7b5a8f9841026c1a752bd857d78f160cad76e66727938dde8199a588ffa1777ddc24b48cb94467cd393c1184656c4e90dc97e98fa3a591ed3ea11657cf3d7a5a', 'Timestamp': '1753897507.983125', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:08,046 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'WIF_USDT', 'entry_price': '0', 'mark_price': '0.9824', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.01', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '50', 'history_pnl': '0', 'risk_limit': '20000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897510, 'update_id': 43, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:45:08,048 [INFO] [core.unified_leverage_manager] 🔍 Gate.io杠杆验证: 设置3倍, 实际3倍
2025-07-31 01:45:08,048 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: gate WIF-USDT -> 3x
2025-07-31 01:45:08,049 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: gate WIF-USDT 3x
2025-07-31 01:45:08,049 [INFO] [core.unified_leverage_manager] 🔧 Gate统一杠杆设置: AI16Z-USDT 3倍
2025-07-31 01:45:08,050 [INFO] [core.unified_leverage_manager] 🔧 Gate.io杠杆设置: 合约=AI16Z_USDT, 杠杆=3倍
2025-07-31 01:45:08,050 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.432秒 (要求: 1.500秒)
2025-07-31 01:45:09,492 [DEBUG] [exchanges.gate_exchange] Gate.io请求: POST https://api.gateio.ws/api/v4/futures/usdt/positions/AI16Z_USDT/leverage?leverage=3, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '0c95fade23d0dad26dcbf87f6dc36a654837ddaf7577b3e7c32ebbf701f1f6299cf2e2c5e55f7b6b0409d2ebbe85be4062c64e42ca7f53444a1895c5e4d7f627', 'Timestamp': '1753897509.4929438', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:09,556 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'AI16Z_USDT', 'entry_price': '0', 'mark_price': '0.1409', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.006', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '75', 'history_pnl': '0', 'risk_limit': '5000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897513, 'update_id': 42, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:45:09,556 [INFO] [core.unified_leverage_manager] ✅ Gate.io杠杆设置成功: AI16Z-USDT 3倍
2025-07-31 01:45:09,758 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.234秒 (要求: 1.500秒)
2025-07-31 01:45:11,004 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/positions/AI16Z_USDT, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '114495f3217e91227a9dd0e111de5affbaa54db0a397b3b27b2e625996243f7bd1f326b7934c5abe610355a251f2d070c88443b86fcefc014f802fb4eca38d31', 'Timestamp': '1753897511.0042026', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:11,068 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'AI16Z_USDT', 'entry_price': '0', 'mark_price': '0.1409', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.006', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '75', 'history_pnl': '0', 'risk_limit': '5000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897513, 'update_id': 42, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:45:11,069 [INFO] [core.unified_leverage_manager] 🔍 Gate.io杠杆验证: 设置3倍, 实际3倍
2025-07-31 01:45:11,070 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: gate AI16Z-USDT -> 3x
2025-07-31 01:45:11,070 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: gate AI16Z-USDT 3x
2025-07-31 01:45:11,071 [INFO] [core.unified_leverage_manager] 🔧 Bybit统一杠杆设置: SPK-USDT 3倍
2025-07-31 01:45:11,071 [INFO] [core.unified_leverage_manager] 🔧 Bybit杠杆设置开始: SPK-USDT -> SPKUSDT 3倍
2025-07-31 01:45:11,072 [DEBUG] [core.unified_leverage_manager] 🔧 Bybit杠杆设置参数: {'category': 'linear', 'symbol': 'SPKUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:11,073 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:45:11,073 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'SPKUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:11,369 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897515595500432 -> 1753897515595ms
2025-07-31 01:45:11,369 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897511369, 服务器时间: 1753897515595, 偏移: 4226ms
2025-07-31 01:45:11,370 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897511370, 偏移=4226, 调整后=1753897515596
2025-07-31 01:45:11,371 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897515596
2025-07-31 01:45:11,371 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "SPKUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:11,372 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:45:11,372 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897515596
2025-07-31 01:45:11,373 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:45:11,373 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:45:11,374 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "SPKUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:11,374 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 119
2025-07-31 01:45:11,375 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 8981c27b35b9444f...
2025-07-31 01:45:11,375 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:45:11,375 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897515596', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '8981c27b35b9444f95164d381ec304862ade047e0375a249f4e8812aaf1e2f75'}
2025-07-31 01:45:11,377 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'SPKUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:11,674 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:45:11,675 [INFO] [core.unified_leverage_manager] ✅ Bybit杠杆设置成功: SPK-USDT 3倍, 响应: {}
2025-07-31 01:45:11,675 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: bybit SPK-USDT -> 3x
2025-07-31 01:45:11,675 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: bybit SPK-USDT 3x
2025-07-31 01:45:11,676 [INFO] [core.unified_leverage_manager] 🔧 Bybit统一杠杆设置: RESOLV-USDT 3倍
2025-07-31 01:45:11,676 [INFO] [core.unified_leverage_manager] 🔧 Bybit杠杆设置开始: RESOLV-USDT -> RESOLVUSDT 3倍
2025-07-31 01:45:11,677 [DEBUG] [core.unified_leverage_manager] 🔧 Bybit杠杆设置参数: {'category': 'linear', 'symbol': 'RESOLVUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:11,677 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 0.895秒 (要求: 1.500秒)
2025-07-31 01:45:12,586 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:45:12,587 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'RESOLVUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:12,884 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897517109701537 -> 1753897517109ms
2025-07-31 01:45:12,884 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897512884, 服务器时间: 1753897517109, 偏移: 4225ms
2025-07-31 01:45:12,885 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897512885, 偏移=4225, 调整后=1753897517110
2025-07-31 01:45:12,885 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897517110
2025-07-31 01:45:12,886 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "RESOLVUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:12,887 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:45:12,887 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897517110
2025-07-31 01:45:12,888 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:45:12,888 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:45:12,889 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "RESOLVUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:12,889 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 122
2025-07-31 01:45:12,890 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 29efd92aa9b856a5...
2025-07-31 01:45:12,890 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:45:12,891 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897517110', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '29efd92aa9b856a56832395438b7476e92038678d5f4d2d9d280a35837aaf456'}
2025-07-31 01:45:12,892 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'RESOLVUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:13,496 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:45:13,497 [INFO] [core.unified_leverage_manager] ✅ Bybit杠杆设置成功: RESOLV-USDT 3倍, 响应: {}
2025-07-31 01:45:13,497 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: bybit RESOLV-USDT -> 3x
2025-07-31 01:45:13,497 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: bybit RESOLV-USDT 3x
2025-07-31 01:45:13,498 [INFO] [core.unified_leverage_manager] 🔧 Bybit统一杠杆设置: ICNT-USDT 3倍
2025-07-31 01:45:13,498 [INFO] [core.unified_leverage_manager] 🔧 Bybit杠杆设置开始: ICNT-USDT -> ICNTUSDT 3倍
2025-07-31 01:45:13,498 [DEBUG] [core.unified_leverage_manager] 🔧 Bybit杠杆设置参数: {'category': 'linear', 'symbol': 'ICNTUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:13,499 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 0.587秒 (要求: 1.500秒)
2025-07-31 01:45:14,090 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:45:14,090 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'ICNTUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:14,696 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897518922087491 -> 1753897518922ms
2025-07-31 01:45:14,697 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897514697, 服务器时间: 1753897518922, 偏移: 4225ms
2025-07-31 01:45:14,697 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897514697, 偏移=4225, 调整后=1753897518922
2025-07-31 01:45:14,698 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897518922
2025-07-31 01:45:14,698 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "ICNTUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:14,698 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:45:14,699 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897518922
2025-07-31 01:45:14,700 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:45:14,700 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:45:14,700 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "ICNTUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:14,701 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 120
2025-07-31 01:45:14,701 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 3c0b8970393ca4bf...
2025-07-31 01:45:14,702 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:45:14,702 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897518922', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '3c0b8970393ca4bf658df741f9afeb87361b42cfef743984751ab0d30cb9dff5'}
2025-07-31 01:45:14,703 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'ICNTUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:15,000 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:45:15,001 [INFO] [core.unified_leverage_manager] ✅ Bybit杠杆设置成功: ICNT-USDT 3倍, 响应: {}
2025-07-31 01:45:15,001 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: bybit ICNT-USDT -> 3x
2025-07-31 01:45:15,002 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: bybit ICNT-USDT 3x
2025-07-31 01:45:15,003 [INFO] [core.unified_leverage_manager] 🔧 Bybit统一杠杆设置: CAKE-USDT 3倍
2025-07-31 01:45:15,003 [INFO] [core.unified_leverage_manager] 🔧 Bybit杠杆设置开始: CAKE-USDT -> CAKEUSDT 3倍
2025-07-31 01:45:15,004 [DEBUG] [core.unified_leverage_manager] 🔧 Bybit杠杆设置参数: {'category': 'linear', 'symbol': 'CAKEUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:15,004 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 0.586秒 (要求: 1.500秒)
2025-07-31 01:45:15,601 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:45:15,602 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'CAKEUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:15,899 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897520124521341 -> 1753897520124ms
2025-07-31 01:45:15,900 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897515900, 服务器时间: 1753897520124, 偏移: 4224ms
2025-07-31 01:45:15,900 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897515900, 偏移=4224, 调整后=1753897520124
2025-07-31 01:45:15,901 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897520124
2025-07-31 01:45:15,901 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "CAKEUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:15,902 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:45:15,903 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897520124
2025-07-31 01:45:15,903 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:45:15,904 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:45:15,904 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "CAKEUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:15,904 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 120
2025-07-31 01:45:15,905 [DEBUG] [exchanges.bybit_exchange]   生成的签名: bd99c85feaf13a12...
2025-07-31 01:45:15,905 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:45:15,906 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897520124', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': 'bd99c85feaf13a1265ea6cb10e7672935a1fa92bbb57eb21ccf63fbb51ec4d44'}
2025-07-31 01:45:15,907 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'CAKEUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:16,204 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:45:16,205 [INFO] [core.unified_leverage_manager] ✅ Bybit杠杆设置成功: CAKE-USDT 3倍, 响应: {}
2025-07-31 01:45:16,205 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: bybit CAKE-USDT -> 3x
2025-07-31 01:45:16,206 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: bybit CAKE-USDT 3x
2025-07-31 01:45:16,206 [INFO] [core.unified_leverage_manager] 🔧 Bybit统一杠杆设置: WIF-USDT 3倍
2025-07-31 01:45:16,207 [INFO] [core.unified_leverage_manager] 🔧 Bybit杠杆设置开始: WIF-USDT -> WIFUSDT 3倍
2025-07-31 01:45:16,207 [DEBUG] [core.unified_leverage_manager] 🔧 Bybit杠杆设置参数: {'category': 'linear', 'symbol': 'WIFUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:16,208 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 0.893秒 (要求: 1.500秒)
2025-07-31 01:45:17,121 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:45:17,121 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'WIFUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:17,726 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897521952557644 -> 1753897521952ms
2025-07-31 01:45:17,727 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897517727, 服务器时间: 1753897521952, 偏移: 4225ms
2025-07-31 01:45:17,728 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897517728, 偏移=4225, 调整后=1753897521953
2025-07-31 01:45:17,728 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897521953
2025-07-31 01:45:17,729 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "WIFUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:17,729 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:45:17,729 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897521953
2025-07-31 01:45:17,730 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:45:17,731 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:45:17,731 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "WIFUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:17,732 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 119
2025-07-31 01:45:17,732 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 10924c8e93f3f921...
2025-07-31 01:45:17,732 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:45:17,733 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897521953', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '10924c8e93f3f9212713320cdd79090807b7a991a15e7f1c0573fb9a7a04086e'}
2025-07-31 01:45:17,734 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'WIFUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:18,032 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:45:18,033 [INFO] [core.unified_leverage_manager] ✅ Bybit杠杆设置成功: WIF-USDT 3倍, 响应: {}
2025-07-31 01:45:18,033 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: bybit WIF-USDT -> 3x
2025-07-31 01:45:18,034 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: bybit WIF-USDT 3x
2025-07-31 01:45:18,034 [INFO] [core.unified_leverage_manager] 🔧 Bybit统一杠杆设置: AI16Z-USDT 3倍
2025-07-31 01:45:18,035 [INFO] [core.unified_leverage_manager] 🔧 Bybit杠杆设置开始: AI16Z-USDT -> AI16ZUSDT 3倍
2025-07-31 01:45:18,035 [DEBUG] [core.unified_leverage_manager] 🔧 Bybit杠杆设置参数: {'category': 'linear', 'symbol': 'AI16ZUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:18,036 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 0.585秒 (要求: 1.500秒)
2025-07-31 01:45:18,627 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:45:18,627 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'AI16ZUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:19,231 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897523458077489 -> 1753897523458ms
2025-07-31 01:45:19,232 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897519232, 服务器时间: 1753897523458, 偏移: 4226ms
2025-07-31 01:45:19,233 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897519233, 偏移=4226, 调整后=1753897523459
2025-07-31 01:45:19,233 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897523459
2025-07-31 01:45:19,234 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "AI16ZUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:19,234 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:45:19,234 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897523459
2025-07-31 01:45:19,235 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:45:19,236 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:45:19,236 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "AI16ZUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:45:19,236 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 121
2025-07-31 01:45:19,237 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 836ec321389537ca...
2025-07-31 01:45:19,237 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:45:19,238 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897523459', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '836ec321389537ca83f37b09ba44ca1c68ef27c77674272250987f9e98f4dcda'}
2025-07-31 01:45:19,239 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'AI16ZUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:45:19,536 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:45:19,537 [INFO] [core.unified_leverage_manager] ✅ Bybit杠杆设置成功: AI16Z-USDT 3倍, 响应: {}
2025-07-31 01:45:19,538 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: bybit AI16Z-USDT -> 3x
2025-07-31 01:45:19,538 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: bybit AI16Z-USDT 3x
2025-07-31 01:45:19,539 [INFO] [core.unified_leverage_manager] 🔧 Okx统一杠杆设置: SPK-USDT 3倍
2025-07-31 01:45:19,539 [WARNING] [core.unified_leverage_manager] ⚠️ OKX杠杆超过安全限制，强制设置为2倍: 3 -> 2
2025-07-31 01:45:19,540 [INFO] [core.unified_leverage_manager] 🔧 OKX杠杆统一设置: 2倍 (与Gate.io、Bybit保持一致)
2025-07-31 01:45:19,540 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:45:19,541 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:23.920Z
2025-07-31 01:45:19,541 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:45:19,654 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:45:19,654 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:45:19,655 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:45:19,655 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.385秒 (要求: 0.500秒)
2025-07-31 01:45:20,040 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:45:20,041 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:24.420Z
2025-07-31 01:45:20,041 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "SPK-USDT-SWAP", "lever": "2", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:45:20,156 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'SPK-USDT-SWAP', 'lever': '2', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:45:20,156 [INFO] [core.unified_leverage_manager] ✅ OKX设置net杠杆: 2倍
2025-07-31 01:45:20,157 [INFO] [core.unified_leverage_manager] ✅ OKX杠杆设置成功: SPK-USDT 2倍 (三交易所统一)
2025-07-31 01:45:20,158 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: okx SPK-USDT -> 3x
2025-07-31 01:45:20,158 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: okx SPK-USDT 3x
2025-07-31 01:45:20,158 [INFO] [core.unified_leverage_manager] 🔧 Okx统一杠杆设置: RESOLV-USDT 3倍
2025-07-31 01:45:20,159 [WARNING] [core.unified_leverage_manager] ⚠️ OKX杠杆超过安全限制，强制设置为2倍: 3 -> 2
2025-07-31 01:45:20,159 [INFO] [core.unified_leverage_manager] 🔧 OKX杠杆统一设置: 2倍 (与Gate.io、Bybit保持一致)
2025-07-31 01:45:20,160 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.380秒 (要求: 0.500秒)
2025-07-31 01:45:20,553 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:45:20,553 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:24.932Z
2025-07-31 01:45:20,554 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:45:20,669 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:45:20,670 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:45:20,671 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:45:20,671 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.382秒 (要求: 0.500秒)
2025-07-31 01:45:21,066 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:45:21,067 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:25.446Z
2025-07-31 01:45:21,067 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "RESOLV-USDT-SWAP", "lever": "2", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:45:21,187 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'RESOLV-USDT-SWAP', 'lever': '2', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:45:21,188 [INFO] [core.unified_leverage_manager] ✅ OKX设置net杠杆: 2倍
2025-07-31 01:45:21,188 [INFO] [core.unified_leverage_manager] ✅ OKX杠杆设置成功: RESOLV-USDT 2倍 (三交易所统一)
2025-07-31 01:45:21,189 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: okx RESOLV-USDT -> 3x
2025-07-31 01:45:21,189 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: okx RESOLV-USDT 3x
2025-07-31 01:45:21,190 [INFO] [core.unified_leverage_manager] 🔧 Okx统一杠杆设置: ICNT-USDT 3倍
2025-07-31 01:45:21,190 [WARNING] [core.unified_leverage_manager] ⚠️ OKX杠杆超过安全限制，强制设置为2倍: 3 -> 2
2025-07-31 01:45:21,190 [INFO] [core.unified_leverage_manager] 🔧 OKX杠杆统一设置: 2倍 (与Gate.io、Bybit保持一致)
2025-07-31 01:45:21,191 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.375秒 (要求: 0.500秒)
2025-07-31 01:45:21,564 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:45:21,564 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:25.944Z
2025-07-31 01:45:21,565 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:45:21,688 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:45:21,688 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:45:21,689 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:45:21,690 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.374秒 (要求: 0.500秒)
2025-07-31 01:45:22,066 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:45:22,067 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:26.446Z
2025-07-31 01:45:22,067 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "ICNT-USDT-SWAP", "lever": "2", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:45:22,184 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:45:22,184 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:45:22,184 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:45:22,185 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:45:22,185 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/set-leverage
2025-07-31 01:45:22,185 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:45:22,185 [INFO] [core.unified_leverage_manager] ✅ OKX设置net杠杆: 2倍
2025-07-31 01:45:22,186 [INFO] [core.unified_leverage_manager] ✅ OKX杠杆设置成功: ICNT-USDT 2倍 (三交易所统一)
2025-07-31 01:45:22,186 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: okx ICNT-USDT -> 3x
2025-07-31 01:45:22,186 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: okx ICNT-USDT 3x
2025-07-31 01:45:22,186 [INFO] [core.unified_leverage_manager] 🔧 Okx统一杠杆设置: CAKE-USDT 3倍
2025-07-31 01:45:22,187 [WARNING] [core.unified_leverage_manager] ⚠️ OKX杠杆超过安全限制，强制设置为2倍: 3 -> 2
2025-07-31 01:45:22,187 [INFO] [core.unified_leverage_manager] 🔧 OKX杠杆统一设置: 2倍 (与Gate.io、Bybit保持一致)
2025-07-31 01:45:22,188 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.378秒 (要求: 0.500秒)
2025-07-31 01:45:22,579 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:45:22,579 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:26.959Z
2025-07-31 01:45:22,580 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:45:22,698 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:45:22,699 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:45:22,699 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:45:22,700 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.379秒 (要求: 0.500秒)
2025-07-31 01:45:23,081 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:45:23,082 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:27.461Z
2025-07-31 01:45:23,083 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "CAKE-USDT-SWAP", "lever": "2", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:45:23,196 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:45:23,197 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:45:23,197 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:45:23,197 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:45:23,198 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/set-leverage
2025-07-31 01:45:23,199 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:45:23,199 [INFO] [core.unified_leverage_manager] ✅ OKX设置net杠杆: 2倍
2025-07-31 01:45:23,200 [INFO] [core.unified_leverage_manager] ✅ OKX杠杆设置成功: CAKE-USDT 2倍 (三交易所统一)
2025-07-31 01:45:23,200 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: okx CAKE-USDT -> 3x
2025-07-31 01:45:23,201 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: okx CAKE-USDT 3x
2025-07-31 01:45:23,202 [INFO] [core.unified_leverage_manager] 🔧 Okx统一杠杆设置: WIF-USDT 3倍
2025-07-31 01:45:23,202 [WARNING] [core.unified_leverage_manager] ⚠️ OKX杠杆超过安全限制，强制设置为2倍: 3 -> 2
2025-07-31 01:45:23,202 [INFO] [core.unified_leverage_manager] 🔧 OKX杠杆统一设置: 2倍 (与Gate.io、Bybit保持一致)
2025-07-31 01:45:23,203 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.378秒 (要求: 0.500秒)
2025-07-31 01:45:23,597 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:45:23,597 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:27.977Z
2025-07-31 01:45:23,598 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:45:23,711 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:45:23,711 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:45:23,712 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:45:23,712 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.385秒 (要求: 0.500秒)
2025-07-31 01:45:24,110 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:45:24,111 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:28.490Z
2025-07-31 01:45:24,111 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "WIF-USDT-SWAP", "lever": "2", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:45:24,227 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'WIF-USDT-SWAP', 'lever': '2', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:45:24,228 [INFO] [core.unified_leverage_manager] ✅ OKX设置net杠杆: 2倍
2025-07-31 01:45:24,228 [INFO] [core.unified_leverage_manager] ✅ OKX杠杆设置成功: WIF-USDT 2倍 (三交易所统一)
2025-07-31 01:45:24,229 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: okx WIF-USDT -> 3x
2025-07-31 01:45:24,229 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: okx WIF-USDT 3x
2025-07-31 01:45:24,229 [INFO] [core.unified_leverage_manager] 🔧 Okx统一杠杆设置: AI16Z-USDT 3倍
2025-07-31 01:45:24,230 [WARNING] [core.unified_leverage_manager] ⚠️ OKX杠杆超过安全限制，强制设置为2倍: 3 -> 2
2025-07-31 01:45:24,231 [INFO] [core.unified_leverage_manager] 🔧 OKX杠杆统一设置: 2倍 (与Gate.io、Bybit保持一致)
2025-07-31 01:45:24,231 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.379秒 (要求: 0.500秒)
2025-07-31 01:45:24,625 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:45:24,625 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:29.005Z
2025-07-31 01:45:24,626 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:45:24,742 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:45:24,743 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:45:24,744 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:45:24,744 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.381秒 (要求: 0.500秒)
2025-07-31 01:45:25,134 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:45:25,135 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:29.514Z
2025-07-31 01:45:25,136 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "AI16Z-USDT-SWAP", "lever": "2", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:45:25,251 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'AI16Z-USDT-SWAP', 'lever': '2', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:45:25,252 [INFO] [core.unified_leverage_manager] ✅ OKX设置net杠杆: 2倍
2025-07-31 01:45:25,253 [INFO] [core.unified_leverage_manager] ✅ OKX杠杆设置成功: AI16Z-USDT 2倍 (三交易所统一)
2025-07-31 01:45:25,253 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: okx AI16Z-USDT -> 3x
2025-07-31 01:45:25,254 [DEBUG] [core.unified_leverage_manager] ✅ 杠杆预热成功: okx AI16Z-USDT 3x
2025-07-31 01:45:25,254 [INFO] [core.unified_leverage_manager] 🎯 杠杆缓存预热完成:
2025-07-31 01:45:25,254 [INFO] [core.unified_leverage_manager]    成功预热: 18个
2025-07-31 01:45:25,255 [INFO] [core.unified_leverage_manager]    失败数量: 0个
2025-07-31 01:45:25,256 [INFO] [core.unified_leverage_manager]    成功率: 100.0%
2025-07-31 01:45:25,256 [INFO] [core.trading_rules_preloader] 🔧 杠杆缓存预热完成: 18个成功
2025-07-31 01:45:25,257 [INFO] [core.trading_rules_preloader] 🔥 缓存预热完成:
2025-07-31 01:45:25,257 [INFO] [core.trading_rules_preloader]    交易规则预热: 48个
2025-07-31 01:45:25,258 [INFO] [core.trading_rules_preloader]    对冲质量缓存预热: 24个
2025-07-31 01:45:25,258 [INFO] [core.trading_rules_preloader]    精度缓存预热: 36个
2025-07-31 01:45:25,259 [INFO] [core.trading_rules_preloader]    保证金缓存预热: 12个
2025-07-31 01:45:25,259 [INFO] [core.trading_rules_preloader]    余额缓存预热: 4个
2025-07-31 01:45:25,260 [INFO] [core.trading_rules_preloader]    🔥 杠杆缓存预热: 18个
2025-07-31 01:45:25,260 [INFO] [core.trading_rules_preloader] 🎯 缓存预热总计: 142个缓存项
2025-07-31 01:45:25,260 [INFO] [core.trading_rules_preloader] ✅ 缓存预热完成，杠杆设置将使用缓存
2025-07-31 01:45:25,261 [INFO] [core.trading_system_initializer] ✅ 成功预加载 60 条交易规则
2025-07-31 01:45:25,262 [INFO] [core.trading_system_initializer] 💰 初始化资金管理器...
2025-07-31 01:45:25,262 [INFO] [fund_management.fund_transfer_service] 从环境变量加载资金划转超时时间: 10秒
2025-07-31 01:45:25,263 [INFO] [fund_management.fund_manager] ✅ 资金管理器初始化 - 🔥 优化版：统一使用ArbitrageEngine余额缓存
2025-07-31 01:45:25,263 [INFO] [fund_management.fund_manager] 开始初始化资金状态...
2025-07-31 01:45:25,264 [INFO] [UnifiedBalanceManager] 设置交易所实例: ['gate', 'bybit', 'okx']
2025-07-31 01:45:25,264 [INFO] [UnifiedBalanceManager] 🔍 [API调用] 余额接口: 统一更新 | 缓存过期，真实API查询余额
2025-07-31 01:45:25,265 [INFO] [CacheMonitor] 💰 [余额缓存] 未命中: system gate_spot_usdt - 需要API获取
2025-07-31 01:45:25,265 [INFO] [CacheMonitor] 💰 [余额缓存] 未命中: system gate_futures_usdt - 需要API获取
2025-07-31 01:45:25,266 [INFO] [CacheMonitor] 💰 [余额缓存] 未命中: system bybit_unified_usdt - 需要API获取
2025-07-31 01:45:25,266 [INFO] [CacheMonitor] 💰 [余额缓存] 未命中: system okx_unified_usdt - 需要API获取
2025-07-31 01:45:25,267 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/spot/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'bab9b13320ee8759e167a99711947f5a8d3b82836e4ad563820782d665e3c296e46bb72b07e127ead5d95202ab6ee052610f47d0a609c4e2691e74b720c7882c', 'Timestamp': '**********.267711', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:25,369 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): [{'currency': 'ICNT', 'available': '0.00425', 'locked': '0', 'update_id': 48}, {'currency': 'HUMA', 'available': '0.00087', 'locked': '0', 'update_id': 14}, {'currency': 'TMAI', 'available': '0.********', 'locked': '0', 'update_id': 53}, {'currency': 'DOGE', 'available': '0.00099', 'locked': '0', 'update_id': 4}, {'currency': 'USDT', 'available': '103.************', 'locked': '0', 'update_id': 1490}, {'currency': 'NEAR', 'available': '0.00799', 'locked': '0', 'update_id': 218}, {'currency': 'GT', 'available': '0.**********', 'locked': '0', 'update_id': 634}, {'currency': 'ALCH', 'available': '0.08', 'locked': '0', 'update_id': 26}, {'currency': 'SPK', 'available': '0.00943', 'locked': '0', 'update_id': 9}, {'currency': 'PEPE', 'available': '0.039', 'locked': '0', 'update_id': 6}, {'currency': '4EVER', 'available': '0.02703235', 'locked': '0', 'update_id': 26}, {'currency': 'GLS', 'available': '0.01086977', 'locked': '0', 'update_id': 219}, {'currency': 'BTC', 'available': '0.00000786', 'locked': '0', 'update_id': 93}, {'currency': 'UNI', 'available': '0.00952', 'locked': '0', 'update_id': 62}, {'currency': 'BNB', 'available': '0.000035', 'locked': '0', 'update_id': 20}, {'currency': 'ETH', 'available': '0.0000901', 'locked': '0', 'update_id': 6}, {'currency': 'RESOLV', 'available': '0.00609', 'locked': '0', 'update_id': 109}, {'currency': 'LINK', 'available': '0.00141', 'locked': '0', 'update_id': 12}, {'currency': 'PI', 'available': '0.00883', 'locked': '0', 'update_id': 4}, {'currency': 'LTC', 'available': '0.0000506', 'locked': '0', 'update_id': 25}, {'currency': 'POINT', 'available': '0.**********', 'locked': '0', 'update_id': 17}, {'currency': 'LAYER', 'available': '0', 'locked': '0', 'update_id': 0}, {'currency': 'ADA', 'available': '0.00216', 'locked': '0', 'update_id': 117}]
2025-07-31 01:45:25,371 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.396秒 (要求: 1.500秒)
2025-07-31 01:45:26,768 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'fd2c6d688a8ee113c1ad1c7794a32222b3c9b28099d7e57574dd0c47c7afa2b5f75ca62632439bebeec3064b471edd4145ce165761c91eaed0d49d3c6bba6bbd', 'Timestamp': '**********.7682927', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:26,836 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'order_margin': '0', 'point': '0', 'bonus': '0', 'history': {'dnw': '-124.********', 'pnl': '262.***********', 'refr': '0', 'point_fee': '0', 'fund': '1.***********', 'bonus_dnw': '0', 'point_refr': '0', 'bonus_offset': '0', 'fee': '-34.********', 'point_dnw': '0', 'cross_settle': '0'}, 'unrealised_pnl': '0', 'total': '104.***********', 'available': '104.***********', 'enable_credit': False, 'in_dual_mode': False, 'currency': 'USDT', 'position_margin': '0', 'user': ********, 'update_time': **********, 'update_id': 1158, 'position_initial_margin': '0', 'maintenance_margin': '0', 'margin_mode': 0, 'enable_evolved_classic': True, 'cross_initial_margin': '0', 'cross_maintenance_margin': '0', 'cross_order_margin': '0', 'cross_unrealised_pnl': '0', 'cross_virtual_unrealised_pnl': '0', 'cross_available': '104.***********', 'isolated_position_margin': '0', 'enable_new_dual_mode': False, 'margin_mode_name': 'classic', 'cross_margin_balance': '104.***********', 'cross_mmr': '0', 'cross_imr': '0', 'enable_tiered_mm': False, 'position_voucher_total': '0'}
2025-07-31 01:45:26,837 [DEBUG] [UnifiedBalanceManager] gate 分离账户: 现货$103.19, 期货$104.64
2025-07-31 01:45:26,838 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-31 01:45:26,838 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 1 → 1 参数
2025-07-31 01:45:27,135 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: *************988401 -> *************ms
2025-07-31 01:45:27,135 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: *************, 服务器时间: *************, 偏移: 4225ms
2025-07-31 01:45:27,136 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=*************, 偏移=4225, 调整后=*************
2025-07-31 01:45:27,136 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: *************
2025-07-31 01:45:27,137 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:45:27,137 [DEBUG] [exchanges.bybit_exchange]   timestamp: *************
2025-07-31 01:45:27,138 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:45:27,138 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:45:27,138 [DEBUG] [exchanges.bybit_exchange]   param_str: accountType=UNIFIED
2025-07-31 01:45:27,139 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 54
2025-07-31 01:45:27,140 [DEBUG] [exchanges.bybit_exchange]   生成的签名: ecb145f4d8569688...
2025-07-31 01:45:27,140 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED
2025-07-31 01:45:27,141 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '*************', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': 'ecb145f4d8569688842bc5d6d6b488d4899d980162e34cb75df3684c09267dfa'}
2025-07-31 01:45:27,141 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'accountType': 'UNIFIED'}
2025-07-31 01:45:27,448 [DEBUG] [exchanges.bybit_exchange] Bybit原始余额响应: {'list': [{'accountIMRate': 0.0, 'totalMaintenanceMarginByMp': '0', 'totalInitialMargin': '0', 'accountType': 'UNIFIED', 'accountMMRate': 0.0, 'accountMMRateByMp': 0.0, 'accountIMRateByMp': 0.0, 'totalInitialMarginByMp': '0', 'totalMaintenanceMargin': '0', 'totalEquity': '525.********', 'totalMarginBalance': 521.********, 'totalAvailableBalance': 521.********, 'totalPerpUPL': '0', 'totalWalletBalance': 521.********, 'accountLTV': '0', 'coin': [{'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0.00984', 'totalPositionMM': '0', 'usdValue': 3.********, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.00984, 'cumRealisedPnl': '-0.00016', 'locked': '0', 'marginCollateral': False, 'coin': 'COINX'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0', 'totalPositionMM': '0', 'usdValue': 0.********, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.0, 'cumRealisedPnl': '-0.********', 'locked': '0', 'marginCollateral': True, 'coin': 'BTC'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '520.********', 'totalPositionMM': '0', 'usdValue': 521.********, 'unrealisedPnl': '0', 'collateralSwitch': True, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 520.********, 'cumRealisedPnl': '-47.09688583', 'locked': '0', 'marginCollateral': True, 'coin': 'USDT'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '1.13228612', 'totalPositionMM': '0', 'usdValue': 0.85700471, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 1.13228612, 'cumRealisedPnl': '0', 'locked': '0', 'marginCollateral': True, 'coin': 'MNT'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0.0324', 'totalPositionMM': '0', 'usdValue': 0.00364853, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.0324, 'cumRealisedPnl': '-4.0635', 'locked': '0', 'marginCollateral': True, 'coin': 'SPK'}]}]}
2025-07-31 01:45:27,450 [INFO] [exchanges.bybit_exchange] Bybit UNIFIED账户总可用余额: $521.05 USD
2025-07-31 01:45:27,450 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED COINX: 钱包=0.009840, 锁定=0.000000, 可用=0.009840
2025-07-31 01:45:27,451 [DEBUG] [exchanges.bybit_exchange] Bybit COINX 最终余额: 可用=0.009840, 锁定=0.000000
2025-07-31 01:45:27,451 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED BTC: 钱包=0.000000, 锁定=0.000000, 可用=0.000000
2025-07-31 01:45:27,452 [INFO] [exchanges.bybit_exchange] Bybit USDT余额详细计算: 钱包余额=520.99, 锁定=0.00, 订单保证金=0.00, 持仓保证金=0.00, 可用余额=520.99
2025-07-31 01:45:27,453 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED USDT: 钱包=520.986453, 锁定=0.000000, 可用=520.986453
2025-07-31 01:45:27,453 [DEBUG] [exchanges.bybit_exchange] Bybit USDT 最终余额: 可用=520.986453, 锁定=0.000000
2025-07-31 01:45:27,454 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED MNT: 钱包=1.132286, 锁定=0.000000, 可用=1.132286
2025-07-31 01:45:27,454 [DEBUG] [exchanges.bybit_exchange] Bybit MNT 最终余额: 可用=1.132286, 锁定=0.000000
2025-07-31 01:45:27,454 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED SPK: 钱包=0.032400, 锁定=0.000000, 可用=0.032400
2025-07-31 01:45:27,455 [DEBUG] [exchanges.bybit_exchange] Bybit SPK 最终余额: 可用=0.032400, 锁定=0.000000
2025-07-31 01:45:27,456 [INFO] [exchanges.bybit_exchange] Bybit账户余额处理完成: 4个币种
2025-07-31 01:45:27,456 [DEBUG] [UnifiedBalanceManager] bybit 统一账户: $520.99
2025-07-31 01:45:27,457 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/balance
2025-07-31 01:45:27,457 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:31.836Z
2025-07-31 01:45:27,458 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:45:27,576 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'adjEq': '', 'availEq': '', 'borrowFroz': '', 'details': [{'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '221.*************', 'availEq': '221.*************', 'borrowFroz': '', 'cashBal': '221.*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '221.*************', 'eqUsd': '221.**************', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1753891303843', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '0.2844993821824943', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.0441', 'availEq': '0.0441', 'borrowFroz': '', 'cashBal': '0.0441', 'ccy': 'SPK', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.0441', 'eqUsd': '0.00496125', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '0.098321391753442', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '0.0441', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '0.0006252766236732', 'spotUplRatio': '0.144206748843765', 'stgyEq': '0', 'totalPnl': '-0.0075851727542471', 'totalPnlRatio': '-0.604568561319909', 'twap': '0', 'uTime': '1753860251957', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.00000000289', 'availEq': '0.00000000289', 'borrowFroz': '', 'cashBal': '0.00000000289', 'ccy': 'BTC', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.00000000289', 'eqUsd': '0.000340478414', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748504762280', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000746', 'availEq': '0.000000746', 'borrowFroz': '', 'cashBal': '0.000000746', 'ccy': 'SOL', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000746', 'eqUsd': '0.00013365336', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748504762277', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.0000697', 'availEq': '0.0000697', 'borrowFroz': '', 'cashBal': '0.0000697', 'ccy': 'ADA', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.0000697', 'eqUsd': '0.00005401053', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1750852693782', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000567', 'availEq': '0.000000567', 'borrowFroz': '', 'cashBal': '0.000000567', 'ccy': 'UNI', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000567', 'eqUsd': '0.000005777163', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1750074898770', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000099', 'availEq': '0.000000099', 'borrowFroz': '', 'cashBal': '0.000000099', 'ccy': 'FIL', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000099', 'eqUsd': '0.000000249678', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1752849679083', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000004', 'availEq': '0.000000004', 'borrowFroz': '', 'cashBal': '0.000000004', 'ccy': 'LINK', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000004', 'eqUsd': '0.000000070812', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748614312217', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000004', 'availEq': '0.000000004', 'borrowFroz': '', 'cashBal': '0.000000004', 'ccy': 'ATOM', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000004', 'eqUsd': '0.000000017764', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748626688438', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.00000000455', 'availEq': '0.00000000455', 'borrowFroz': '', 'cashBal': '0.00000000455', 'ccy': 'NEAR', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.00000000455', 'eqUsd': '0.00000001215305', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1750157106950', 'upl': '0', 'uplLiab': ''}], 'imr': '', 'isoEq': '0', 'mgnRatio': '', 'mmr': '', 'notionalUsd': '', 'notionalUsdForBorrow': '', 'notionalUsdForFutures': '', 'notionalUsdForOption': '', 'notionalUsdForSwap': '', 'ordFroz': '', 'totalEq': '221.5596692229117', 'uTime': '1753897531896', 'upl': ''}], 'msg': ''}
2025-07-31 01:45:27,580 [DEBUG] [exchanges.okx_exchange] OKX使用统一账户API获取余额成功
2025-07-31 01:45:27,580 [DEBUG] [exchanges.okx_exchange] OKX检测到统一账户API响应格式
2025-07-31 01:45:27,580 [DEBUG] [exchanges.okx_exchange] OKX统一账户 USDT 余额: 可用=221.*************, 冻结=0.0
2025-07-31 01:45:27,581 [DEBUG] [exchanges.okx_exchange] OKX统一账户 SPK 余额: 可用=0.0441, 冻结=0.0
2025-07-31 01:45:27,581 [DEBUG] [exchanges.okx_exchange] OKX统一账户 BTC 余额: 可用=2.89e-09, 冻结=0.0
2025-07-31 01:45:27,581 [DEBUG] [exchanges.okx_exchange] OKX统一账户 SOL 余额: 可用=7.46e-07, 冻结=0.0
2025-07-31 01:45:27,581 [DEBUG] [exchanges.okx_exchange] OKX统一账户 ADA 余额: 可用=6.97e-05, 冻结=0.0
2025-07-31 01:45:27,582 [DEBUG] [exchanges.okx_exchange] OKX统一账户 UNI 余额: 可用=5.67e-07, 冻结=0.0
2025-07-31 01:45:27,582 [DEBUG] [exchanges.okx_exchange] OKX统一账户 FIL 余额: 可用=9.9e-08, 冻结=0.0
2025-07-31 01:45:27,582 [DEBUG] [exchanges.okx_exchange] OKX统一账户 LINK 余额: 可用=4e-09, 冻结=0.0
2025-07-31 01:45:27,583 [DEBUG] [exchanges.okx_exchange] OKX统一账户 ATOM 余额: 可用=4e-09, 冻结=0.0
2025-07-31 01:45:27,583 [DEBUG] [exchanges.okx_exchange] OKX统一账户 NEAR 余额: 可用=4.55e-09, 冻结=0.0
2025-07-31 01:45:27,584 [DEBUG] [exchanges.okx_exchange] OKX余额解析完成: {'USDT': {'available': 221.*************, 'locked': 0.0}, 'SPK': {'available': 0.0441, 'locked': 0.0}, 'BTC': {'available': 2.89e-09, 'locked': 0.0}, 'SOL': {'available': 7.46e-07, 'locked': 0.0}, 'ADA': {'available': 6.97e-05, 'locked': 0.0}, 'UNI': {'available': 5.67e-07, 'locked': 0.0}, 'FIL': {'available': 9.9e-08, 'locked': 0.0}, 'LINK': {'available': 4e-09, 'locked': 0.0}, 'ATOM': {'available': 4e-09, 'locked': 0.0}, 'NEAR': {'available': 4.55e-09, 'locked': 0.0}}
2025-07-31 01:45:27,584 [DEBUG] [UnifiedBalanceManager] okx 统一账户: $221.55
2025-07-31 01:45:27,585 [INFO] [UnifiedBalanceManager] ✅ 余额缓存更新完成: 4个账户
2025-07-31 01:45:27,585 [INFO] [fund_management.fund_manager] ✅ [统一模块] 余额查询: UnifiedBalanceManager | 删除重复逻辑，强制使用统一模块
2025-07-31 01:45:27,585 [INFO] [UnifiedBalanceManager] 设置交易所实例: ['gate', 'bybit', 'okx']
2025-07-31 01:45:27,586 [DEBUG] [UnifiedBalanceManager] ✅ [缓存命中] 余额缓存: UnifiedBalanceManager | 余额数据从缓存获取，零延迟
2025-07-31 01:45:27,586 [INFO] [CacheMonitor] 💰 [余额缓存] 命中: system gate_spot_usdt = $103.19
2025-07-31 01:45:27,586 [INFO] [CacheMonitor] 💰 [余额缓存] 命中: system gate_futures_usdt = $104.64
2025-07-31 01:45:27,587 [INFO] [CacheMonitor] 💰 [余额缓存] 命中: system bybit_unified_usdt = $520.99
2025-07-31 01:45:27,587 [INFO] [CacheMonitor] 💰 [余额缓存] 命中: system okx_unified_usdt = $221.55
2025-07-31 01:45:27,588 [INFO] [fund_management.fund_manager] ✅ [统一模块] 余额查询: UnifiedBalanceManager | 删除重复逻辑，强制使用统一模块
2025-07-31 01:45:27,588 [DEBUG] [fund_management.fund_manager] gate总USDT余额: 207.83
2025-07-31 01:45:27,588 [DEBUG] [fund_management.fund_manager] bybit总USDT余额: 520.99
2025-07-31 01:45:27,589 [DEBUG] [fund_management.fund_manager] okx总USDT余额: 221.55
2025-07-31 01:45:27,589 [DEBUG] [fund_management.fund_manager] 余额平衡检查: 最大520.99, 最小207.83, 差异比例60.1%, 需要调整: True
2025-07-31 01:45:27,590 [INFO] [fund_management.fund_manager] 🔄 检测到资金不平衡，开始调整...
2025-07-31 01:45:27,590 [INFO] [fund_management.fund_manager] 🔄 执行资金平衡调整...
2025-07-31 01:45:27,591 [INFO] [fund_management.fund_manager] ✅ 资金平衡调整完成
2025-07-31 01:45:27,591 [INFO] [fund_management.fund_manager] 资金状态初始化完成
2025-07-31 01:45:27,591 [INFO] [core.trading_system_initializer] ✅ 资金管理器初始化成功
2025-07-31 01:45:27,592 [INFO] [core.trading_system_initializer] 📋 步骤2.3: 确保交易对信息完全就绪...
2025-07-31 01:45:27,592 [INFO] [core.universal_token_system] 📋 使用缓存的交易对: 10个
2025-07-31 01:45:27,592 [INFO] [core.trading_system_initializer] ✅ 交易对信息就绪: 10个交易对
2025-07-31 01:45:27,592 [INFO] [api_optimizer] 🚀 开始分批预加载API调用优化...
2025-07-31 01:45:27,593 [INFO] [api_optimizer] 📦 开始分批预加载交易规则: 每批5个，批间冷却2.0秒
2025-07-31 01:45:27,593 [INFO] [api_optimizer] 📊 总任务数: 60, 分为12批
2025-07-31 01:45:27,593 [INFO] [api_optimizer] 🔄 处理第1/12批 (5个任务)
2025-07-31 01:45:27,594 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:27,594 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.125秒 (间隔要求: 0.125秒)
2025-07-31 01:45:27,594 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.125秒 (间隔要求: 0.125秒)
2025-07-31 01:45:27,595 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.124秒 (间隔要求: 0.125秒)
2025-07-31 01:45:27,595 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.124秒 (间隔要求: 0.125秒)
2025-07-31 01:45:27,735 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:27,736 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:27,736 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:27,736 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:27,737 [INFO] [api_optimizer] ✅ 第1批完成: 5/5成功, 耗时0.1秒
2025-07-31 01:45:27,737 [INFO] [api_optimizer] 🕐 批间冷却: 2.0秒...
2025-07-31 01:45:29,748 [INFO] [api_optimizer] 🔄 处理第2/12批 (5个任务)
2025-07-31 01:45:29,749 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:29,749 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.125秒 (间隔要求: 0.125秒)
2025-07-31 01:45:29,750 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.124秒 (间隔要求: 0.125秒)
2025-07-31 01:45:29,751 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.124秒 (间隔要求: 0.125秒)
2025-07-31 01:45:29,751 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.123秒 (间隔要求: 0.125秒)
2025-07-31 01:45:29,890 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:29,890 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:29,891 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:29,891 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:29,892 [INFO] [api_optimizer] ✅ 第2批完成: 5/5成功, 耗时0.1秒
2025-07-31 01:45:29,892 [INFO] [api_optimizer] 🕐 批间冷却: 2.0秒...
2025-07-31 01:45:31,894 [INFO] [api_optimizer] 🔄 处理第3/12批 (5个任务)
2025-07-31 01:45:31,895 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:31,896 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.124秒 (间隔要求: 0.125秒)
2025-07-31 01:45:31,896 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.123秒 (间隔要求: 0.125秒)
2025-07-31 01:45:31,897 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.123秒 (间隔要求: 0.125秒)
2025-07-31 01:45:31,898 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.122秒 (间隔要求: 0.125秒)
2025-07-31 01:45:32,033 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:32,033 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:32,034 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:32,034 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:32,035 [INFO] [api_optimizer] ✅ 第3批完成: 5/5成功, 耗时0.1秒
2025-07-31 01:45:32,035 [INFO] [api_optimizer] 🕐 批间冷却: 2.0秒...
2025-07-31 01:45:34,041 [INFO] [api_optimizer] 🔄 处理第4/12批 (5个任务)
2025-07-31 01:45:34,042 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:34,042 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.125秒 (间隔要求: 0.125秒)
2025-07-31 01:45:34,043 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.124秒 (间隔要求: 0.125秒)
2025-07-31 01:45:34,043 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.123秒 (间隔要求: 0.125秒)
2025-07-31 01:45:34,044 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.122秒 (间隔要求: 0.125秒)
2025-07-31 01:45:34,164 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:34,165 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:34,165 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:34,166 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.000秒
2025-07-31 01:45:34,166 [INFO] [api_optimizer] ✅ 第4批完成: 5/5成功, 耗时0.1秒
2025-07-31 01:45:34,167 [INFO] [api_optimizer] 🕐 批间冷却: 2.0秒...
2025-07-31 01:45:36,166 [INFO] [api_optimizer] 🔄 处理第5/12批 (5个任务)
2025-07-31 01:45:36,167 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:36,167 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.249秒 (间隔要求: 0.250秒)
2025-07-31 01:45:36,168 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.249秒 (间隔要求: 0.250秒)
2025-07-31 01:45:36,168 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:45:36,168 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:45:36,419 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:36,419 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:36,420 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:36,420 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:36,421 [INFO] [api_optimizer] ✅ 第5批完成: 5/5成功, 耗时0.3秒
2025-07-31 01:45:36,421 [INFO] [api_optimizer] 🕐 批间冷却: 2.0秒...
2025-07-31 01:45:38,434 [INFO] [api_optimizer] 🔄 处理第6/12批 (5个任务)
2025-07-31 01:45:38,435 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:38,435 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.250秒 (间隔要求: 0.250秒)
2025-07-31 01:45:38,436 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.250秒 (间隔要求: 0.250秒)
2025-07-31 01:45:38,436 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.249秒 (间隔要求: 0.250秒)
2025-07-31 01:45:38,436 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.249秒 (间隔要求: 0.250秒)
2025-07-31 01:45:38,687 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:38,688 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:38,688 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:38,688 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:38,689 [INFO] [api_optimizer] ✅ 第6批完成: 5/5成功, 耗时0.3秒
2025-07-31 01:45:38,689 [INFO] [api_optimizer] 🕐 批间冷却: 2.0秒...
2025-07-31 01:45:40,699 [INFO] [api_optimizer] 🔄 处理第7/12批 (5个任务)
2025-07-31 01:45:40,700 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:40,700 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.250秒 (间隔要求: 0.250秒)
2025-07-31 01:45:40,701 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.249秒 (间隔要求: 0.250秒)
2025-07-31 01:45:40,702 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:45:40,702 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:45:40,977 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:40,977 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:40,978 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:40,979 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:40,979 [INFO] [api_optimizer] ✅ 第7批完成: 5/5成功, 耗时0.3秒
2025-07-31 01:45:40,980 [INFO] [api_optimizer] 🕐 批间冷却: 2.0秒...
2025-07-31 01:45:42,997 [INFO] [api_optimizer] 🔄 处理第8/12批 (5个任务)
2025-07-31 01:45:42,997 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:42,998 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.249秒 (间隔要求: 0.250秒)
2025-07-31 01:45:42,998 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.249秒 (间隔要求: 0.250秒)
2025-07-31 01:45:42,998 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.249秒 (间隔要求: 0.250秒)
2025-07-31 01:45:42,999 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:45:43,265 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:43,265 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:43,266 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:43,266 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.000秒
2025-07-31 01:45:43,267 [INFO] [api_optimizer] ✅ 第8批完成: 5/5成功, 耗时0.3秒
2025-07-31 01:45:43,267 [INFO] [api_optimizer] 🕐 批间冷却: 2.0秒...
2025-07-31 01:45:45,281 [INFO] [api_optimizer] 🔄 处理第9/12批 (5个任务)
2025-07-31 01:45:45,282 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:45,282 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.500秒 (间隔要求: 0.500秒)
2025-07-31 01:45:45,283 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.499秒 (间隔要求: 0.500秒)
2025-07-31 01:45:45,284 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.499秒 (间隔要求: 0.500秒)
2025-07-31 01:45:45,284 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.498秒 (间隔要求: 0.500秒)
2025-07-31 01:45:45,780 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:45,780 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:45,781 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:45,781 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:45,782 [INFO] [api_optimizer] ✅ 第9批完成: 5/5成功, 耗时0.5秒
2025-07-31 01:45:45,782 [INFO] [api_optimizer] 🕐 批间冷却: 2.0秒...
2025-07-31 01:45:47,808 [INFO] [api_optimizer] 🔄 处理第10/12批 (5个任务)
2025-07-31 01:45:47,809 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:47,809 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.500秒 (间隔要求: 0.500秒)
2025-07-31 01:45:47,809 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.500秒 (间隔要求: 0.500秒)
2025-07-31 01:45:47,809 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.500秒 (间隔要求: 0.500秒)
2025-07-31 01:45:47,810 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.499秒 (间隔要求: 0.500秒)
2025-07-31 01:45:48,310 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:48,311 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:48,311 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:48,312 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:48,312 [INFO] [api_optimizer] ✅ 第10批完成: 5/5成功, 耗时0.5秒
2025-07-31 01:45:48,313 [INFO] [api_optimizer] 🕐 批间冷却: 2.0秒...
2025-07-31 01:45:50,330 [INFO] [api_optimizer] 🔄 处理第11/12批 (5个任务)
2025-07-31 01:45:50,331 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:50,331 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.500秒 (间隔要求: 0.500秒)
2025-07-31 01:45:50,332 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.499秒 (间隔要求: 0.500秒)
2025-07-31 01:45:50,333 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.498秒 (间隔要求: 0.500秒)
2025-07-31 01:45:50,333 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.498秒 (间隔要求: 0.500秒)
2025-07-31 01:45:50,844 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:50,845 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:50,845 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:50,846 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:50,846 [INFO] [api_optimizer] ✅ 第11批完成: 5/5成功, 耗时0.5秒
2025-07-31 01:45:50,846 [INFO] [api_optimizer] 🕐 批间冷却: 2.0秒...
2025-07-31 01:45:52,864 [INFO] [api_optimizer] 🔄 处理第12/12批 (5个任务)
2025-07-31 01:45:52,865 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.001秒
2025-07-31 01:45:52,865 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.499秒 (间隔要求: 0.500秒)
2025-07-31 01:45:52,865 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.498秒 (间隔要求: 0.500秒)
2025-07-31 01:45:52,865 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.498秒 (间隔要求: 0.500秒)
2025-07-31 01:45:52,866 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.498秒 (间隔要求: 0.500秒)
2025-07-31 01:45:53,375 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:53,375 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:53,376 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:53,377 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.000秒
2025-07-31 01:45:53,377 [INFO] [api_optimizer] ✅ 第12批完成: 5/5成功, 耗时0.5秒
2025-07-31 01:45:53,378 [INFO] [api_optimizer] ✅ 分批预加载交易规则完成
2025-07-31 01:45:53,378 [INFO] [api_optimizer] 💰 批量化余额查询...
2025-07-31 01:45:53,379 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:45:53,380 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=spot, 强制使用category=UNIFIED
2025-07-31 01:45:53,380 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:45:53,381 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.496秒 (间隔要求: 0.500秒)
2025-07-31 01:45:53,874 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.500秒 (要求: 0.500秒)
2025-07-31 01:45:54,381 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/balance
2025-07-31 01:45:54,382 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:45:58.761Z
2025-07-31 01:45:54,382 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:45:54,497 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'adjEq': '', 'availEq': '', 'borrowFroz': '', 'details': [{'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '221.*************', 'availEq': '221.*************', 'borrowFroz': '', 'cashBal': '221.*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '221.*************', 'eqUsd': '221.**************', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1753891303843', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '0.2844993821824943', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.0441', 'availEq': '0.0441', 'borrowFroz': '', 'cashBal': '0.0441', 'ccy': 'SPK', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.0441', 'eqUsd': '0.00496125', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '0.098321391753442', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '0.0441', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '0.0006252766236732', 'spotUplRatio': '0.144206748843765', 'stgyEq': '0', 'totalPnl': '-0.0075851727542471', 'totalPnlRatio': '-0.604568561319909', 'twap': '0', 'uTime': '1753860251957', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.00000000289', 'availEq': '0.00000000289', 'borrowFroz': '', 'cashBal': '0.00000000289', 'ccy': 'BTC', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.00000000289', 'eqUsd': '0.000340484483', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748504762280', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000746', 'availEq': '0.000000746', 'borrowFroz': '', 'cashBal': '0.000000746', 'ccy': 'SOL', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000746', 'eqUsd': '0.00013363844', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748504762277', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.0000697', 'availEq': '0.0000697', 'borrowFroz': '', 'cashBal': '0.0000697', 'ccy': 'ADA', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.0000697', 'eqUsd': '0.0000540175', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1750852693782', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000567', 'availEq': '0.000000567', 'borrowFroz': '', 'cashBal': '0.000000567', 'ccy': 'UNI', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000567', 'eqUsd': '0.000005777163', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1750074898770', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000099', 'availEq': '0.000000099', 'borrowFroz': '', 'cashBal': '0.000000099', 'ccy': 'FIL', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000099', 'eqUsd': '0.000000249678', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1752849679083', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000004', 'availEq': '0.000000004', 'borrowFroz': '', 'cashBal': '0.000000004', 'ccy': 'LINK', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000004', 'eqUsd': '0.000000070808', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748614312217', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.000000004', 'availEq': '0.000000004', 'borrowFroz': '', 'cashBal': '0.000000004', 'ccy': 'ATOM', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.000000004', 'eqUsd': '0.000000017772', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1748626688438', 'upl': '0', 'uplLiab': ''}, {'accAvgPx': '', 'autoLendMtAmt': '0', 'autoLendStatus': 'unsupported', 'availBal': '0.00000000455', 'availEq': '0.00000000455', 'borrowFroz': '', 'cashBal': '0.00000000455', 'ccy': 'NEAR', 'clSpotInUseAmt': '', 'colBorrAutoConversion': '0', 'collateralEnabled': False, 'collateralRestrict': False, 'crossLiab': '', 'disEq': '0', 'eq': '0.00000000455', 'eqUsd': '0.0000000121576', 'fixedBal': '0', 'frozenBal': '0', 'imr': '0', 'interest': '', 'isoEq': '0', 'isoLiab': '', 'isoUpl': '0', 'liab': '', 'maxLoan': '', 'maxSpotInUse': '', 'mgnRatio': '', 'mmr': '0', 'notionalLever': '0', 'openAvgPx': '', 'ordFrozen': '0', 'rewardBal': '0', 'smtSyncEq': '0', 'spotBal': '', 'spotCopyTradingEq': '0', 'spotInUseAmt': '', 'spotIsoBal': '0', 'spotUpl': '', 'spotUplRatio': '', 'stgyEq': '0', 'totalPnl': '', 'totalPnlRatio': '', 'twap': '0', 'uTime': '1750157106950', 'upl': '0', 'uplLiab': ''}], 'imr': '', 'isoEq': '0', 'mgnRatio': '', 'mmr': '', 'notionalUsd': '', 'notionalUsdForBorrow': '', 'notionalUsdForFutures': '', 'notionalUsdForOption': '', 'notionalUsdForSwap': '', 'ordFroz': '', 'totalEq': '221.5596692210393', 'uTime': '1753897558820', 'upl': ''}], 'msg': ''}
2025-07-31 01:45:54,502 [DEBUG] [exchanges.okx_exchange] OKX使用统一账户API获取余额成功
2025-07-31 01:45:54,502 [DEBUG] [exchanges.okx_exchange] OKX检测到统一账户API响应格式
2025-07-31 01:45:54,502 [DEBUG] [exchanges.okx_exchange] OKX统一账户 USDT 余额: 可用=221.*************, 冻结=0.0
2025-07-31 01:45:54,503 [DEBUG] [exchanges.okx_exchange] OKX统一账户 SPK 余额: 可用=0.0441, 冻结=0.0
2025-07-31 01:45:54,503 [DEBUG] [exchanges.okx_exchange] OKX统一账户 BTC 余额: 可用=2.89e-09, 冻结=0.0
2025-07-31 01:45:54,504 [DEBUG] [exchanges.okx_exchange] OKX统一账户 SOL 余额: 可用=7.46e-07, 冻结=0.0
2025-07-31 01:45:54,505 [DEBUG] [exchanges.okx_exchange] OKX统一账户 ADA 余额: 可用=6.97e-05, 冻结=0.0
2025-07-31 01:45:54,505 [DEBUG] [exchanges.okx_exchange] OKX统一账户 UNI 余额: 可用=5.67e-07, 冻结=0.0
2025-07-31 01:45:54,505 [DEBUG] [exchanges.okx_exchange] OKX统一账户 FIL 余额: 可用=9.9e-08, 冻结=0.0
2025-07-31 01:45:54,506 [DEBUG] [exchanges.okx_exchange] OKX统一账户 LINK 余额: 可用=4e-09, 冻结=0.0
2025-07-31 01:45:54,506 [DEBUG] [exchanges.okx_exchange] OKX统一账户 ATOM 余额: 可用=4e-09, 冻结=0.0
2025-07-31 01:45:54,507 [DEBUG] [exchanges.okx_exchange] OKX统一账户 NEAR 余额: 可用=4.55e-09, 冻结=0.0
2025-07-31 01:45:54,507 [DEBUG] [exchanges.okx_exchange] OKX余额解析完成: {'USDT': {'available': 221.*************, 'locked': 0.0}, 'SPK': {'available': 0.0441, 'locked': 0.0}, 'BTC': {'available': 2.89e-09, 'locked': 0.0}, 'SOL': {'available': 7.46e-07, 'locked': 0.0}, 'ADA': {'available': 6.97e-05, 'locked': 0.0}, 'UNI': {'available': 5.67e-07, 'locked': 0.0}, 'FIL': {'available': 9.9e-08, 'locked': 0.0}, 'LINK': {'available': 4e-09, 'locked': 0.0}, 'ATOM': {'available': 4e-09, 'locked': 0.0}, 'NEAR': {'available': 4.55e-09, 'locked': 0.0}}
2025-07-31 01:45:54,508 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.635秒
2025-07-31 01:45:54,894 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/spot/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '94b405016d71bceff2f3c085a0c0c796843ca28a6f0cb65145d9b9a42b304c382f7a8db6f790c379a8bd5e0ea81646c11fb27a4dea38f97ca187797ba142689b', 'Timestamp': '**********.8944426', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:54,895 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 1 → 1 参数
2025-07-31 01:45:55,075 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): [{'currency': 'ICNT', 'available': '0.00425', 'locked': '0', 'update_id': 48}, {'currency': 'HUMA', 'available': '0.00087', 'locked': '0', 'update_id': 14}, {'currency': 'TMAI', 'available': '0.********', 'locked': '0', 'update_id': 53}, {'currency': 'DOGE', 'available': '0.00099', 'locked': '0', 'update_id': 4}, {'currency': 'USDT', 'available': '103.************', 'locked': '0', 'update_id': 1490}, {'currency': 'NEAR', 'available': '0.00799', 'locked': '0', 'update_id': 218}, {'currency': 'GT', 'available': '0.**********', 'locked': '0', 'update_id': 634}, {'currency': 'ALCH', 'available': '0.08', 'locked': '0', 'update_id': 26}, {'currency': 'SPK', 'available': '0.00943', 'locked': '0', 'update_id': 9}, {'currency': 'PEPE', 'available': '0.039', 'locked': '0', 'update_id': 6}, {'currency': '4EVER', 'available': '0.02703235', 'locked': '0', 'update_id': 26}, {'currency': 'GLS', 'available': '0.01086977', 'locked': '0', 'update_id': 219}, {'currency': 'BTC', 'available': '0.00000786', 'locked': '0', 'update_id': 93}, {'currency': 'UNI', 'available': '0.00952', 'locked': '0', 'update_id': 62}, {'currency': 'BNB', 'available': '0.000035', 'locked': '0', 'update_id': 20}, {'currency': 'ETH', 'available': '0.0000901', 'locked': '0', 'update_id': 6}, {'currency': 'RESOLV', 'available': '0.00609', 'locked': '0', 'update_id': 109}, {'currency': 'LINK', 'available': '0.00141', 'locked': '0', 'update_id': 12}, {'currency': 'PI', 'available': '0.00883', 'locked': '0', 'update_id': 4}, {'currency': 'LTC', 'available': '0.0000506', 'locked': '0', 'update_id': 25}, {'currency': 'POINT', 'available': '0.**********', 'locked': '0', 'update_id': 17}, {'currency': 'LAYER', 'available': '0', 'locked': '0', 'update_id': 0}, {'currency': 'ADA', 'available': '0.00216', 'locked': '0', 'update_id': 117}]
2025-07-31 01:45:55,076 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 1.697秒
2025-07-31 01:45:55,499 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897559725277871 -> 1753897559725ms
2025-07-31 01:45:55,500 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897555500, 服务器时间: 1753897559725, 偏移: 4225ms
2025-07-31 01:45:55,501 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897555501, 偏移=4225, 调整后=*************
2025-07-31 01:45:55,501 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: *************
2025-07-31 01:45:55,502 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:45:55,502 [DEBUG] [exchanges.bybit_exchange]   timestamp: *************
2025-07-31 01:45:55,502 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:45:55,503 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:45:55,503 [DEBUG] [exchanges.bybit_exchange]   param_str: accountType=UNIFIED
2025-07-31 01:45:55,504 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 54
2025-07-31 01:45:55,505 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 5cbd0280e712c016...
2025-07-31 01:45:55,505 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED
2025-07-31 01:45:55,505 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '*************', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '5cbd0280e712c0168d6f97eddcfaa34394ed73584171f6595ef26834566490aa'}
2025-07-31 01:45:55,506 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'accountType': 'UNIFIED'}
2025-07-31 01:45:55,812 [DEBUG] [exchanges.bybit_exchange] Bybit原始余额响应: {'list': [{'accountIMRate': 0.0, 'totalMaintenanceMarginByMp': '0', 'totalInitialMargin': '0', 'accountType': 'UNIFIED', 'accountMMRate': 0.0, 'accountMMRateByMp': 0.0, 'accountIMRateByMp': 0.0, 'totalInitialMarginByMp': '0', 'totalMaintenanceMargin': '0', 'totalEquity': '525.********', 'totalMarginBalance': 521.********, 'totalAvailableBalance': 521.********, 'totalPerpUPL': '0', 'totalWalletBalance': 521.********, 'accountLTV': '0', 'coin': [{'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0.00984', 'totalPositionMM': '0', 'usdValue': 3.********, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.00984, 'cumRealisedPnl': '-0.00016', 'locked': '0', 'marginCollateral': False, 'coin': 'COINX'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0', 'totalPositionMM': '0', 'usdValue': 0.********, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.0, 'cumRealisedPnl': '-0.********', 'locked': '0', 'marginCollateral': True, 'coin': 'BTC'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '520.********', 'totalPositionMM': '0', 'usdValue': 521.********, 'unrealisedPnl': '0', 'collateralSwitch': True, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 520.********, 'cumRealisedPnl': '-47.09688583', 'locked': '0', 'marginCollateral': True, 'coin': 'USDT'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '1.13228612', 'totalPositionMM': '0', 'usdValue': 0.85824796, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 1.13228612, 'cumRealisedPnl': '0', 'locked': '0', 'marginCollateral': True, 'coin': 'MNT'}, {'availableToBorrow': '', 'bonus': '0', 'accruedInterest': '0', 'availableToWithdraw': '', 'totalOrderIM': '0', 'equity': '0.0324', 'totalPositionMM': '0', 'usdValue': 0.00364419, 'unrealisedPnl': '0', 'collateralSwitch': False, 'spotHedgingQty': '0', 'borrowAmount': 0.0, 'totalPositionIM': '0', 'walletBalance': 0.0324, 'cumRealisedPnl': '-4.0635', 'locked': '0', 'marginCollateral': True, 'coin': 'SPK'}]}]}
2025-07-31 01:45:55,813 [INFO] [exchanges.bybit_exchange] Bybit UNIFIED账户总可用余额: $521.06 USD
2025-07-31 01:45:55,814 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED COINX: 钱包=0.009840, 锁定=0.000000, 可用=0.009840
2025-07-31 01:45:55,814 [DEBUG] [exchanges.bybit_exchange] Bybit COINX 最终余额: 可用=0.009840, 锁定=0.000000
2025-07-31 01:45:55,815 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED BTC: 钱包=0.000000, 锁定=0.000000, 可用=0.000000
2025-07-31 01:45:55,816 [INFO] [exchanges.bybit_exchange] Bybit USDT余额详细计算: 钱包余额=520.99, 锁定=0.00, 订单保证金=0.00, 持仓保证金=0.00, 可用余额=520.99
2025-07-31 01:45:55,816 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED USDT: 钱包=520.986453, 锁定=0.000000, 可用=520.986453
2025-07-31 01:45:55,817 [DEBUG] [exchanges.bybit_exchange] Bybit USDT 最终余额: 可用=520.986453, 锁定=0.000000
2025-07-31 01:45:55,817 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED MNT: 钱包=1.132286, 锁定=0.000000, 可用=1.132286
2025-07-31 01:45:55,818 [DEBUG] [exchanges.bybit_exchange] Bybit MNT 最终余额: 可用=1.132286, 锁定=0.000000
2025-07-31 01:45:55,818 [DEBUG] [exchanges.bybit_exchange] Bybit UNIFIED SPK: 钱包=0.032400, 锁定=0.000000, 可用=0.032400
2025-07-31 01:45:55,819 [DEBUG] [exchanges.bybit_exchange] Bybit SPK 最终余额: 可用=0.032400, 锁定=0.000000
2025-07-31 01:45:55,820 [INFO] [exchanges.bybit_exchange] Bybit账户余额处理完成: 4个币种
2025-07-31 01:45:55,820 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.440秒
2025-07-31 01:45:55,821 [INFO] [api_optimizer] ✅ 余额查询完成: 3/3
2025-07-31 01:45:55,821 [INFO] [api_optimizer] 📦 开始分批获取合约信息: 每批5个，批间冷却2.0秒
2025-07-31 01:45:55,822 [INFO] [api_optimizer] 📊 合约信息任务数: 30, 分为6批
2025-07-31 01:45:55,822 [INFO] [api_optimizer] 🔄 处理合约信息第1/6批
2025-07-31 01:45:55,822 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: SPK_USDT
2025-07-31 01:45:55,823 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:45:55,823 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.124秒 (间隔要求: 0.125秒)
2025-07-31 01:45:55,824 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.123秒 (间隔要求: 0.125秒)
2025-07-31 01:45:55,824 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.123秒 (间隔要求: 0.125秒)
2025-07-31 01:45:55,825 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.122秒 (间隔要求: 0.125秒)
2025-07-31 01:45:55,949 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: WIF_USDT
2025-07-31 01:45:55,949 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:45:55,950 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: ICNT_USDT
2025-07-31 01:45:55,950 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:45:55,951 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: CAKE_USDT
2025-07-31 01:45:55,951 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:45:55,952 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: RESOLV_USDT
2025-07-31 01:45:55,952 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:45:57,328 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/SPK_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '42797bf16241067be9a317cabc59f058cda1ca51566b0c5e93483b12b1dca12accf5dc3578c596377b21d8c05f112a998afdda7768f820ebe89d765c0c9a088b', 'Timestamp': '1753897557.3276873', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:57,400 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '-0.009055', 'mark_price_round': '0.00001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '5000', 'interest_rate': '0.0003', 'index_price': '0.112524', 'order_price_round': '0.00001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'SPK_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.2', 'maintenance_rate': '0.008333', 'mark_type': 'index', 'funding_interval': 14400, 'type': 'direct', 'risk_limit_step': '9995000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '-0.009055', 'last_price': '0.11242', 'mark_price': '0.11228', 'order_size_max': 1000000, 'funding_next_apply': 1753905600, 'short_users': 688, 'config_change_time': 1753427642, 'create_time': 1750151108, 'trade_size': 200534305, 'position_size': 152858, 'long_users': 644, 'quanto_multiplier': '100', 'funding_impact_value': '5000', 'leverage_max': '75', 'cross_leverage_default': '10', 'risk_limit_max': '10000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 4255275, 'orderbook_id': 322417878, 'funding_cap_ratio': '4', 'voucher_leverage': '2', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1750151108}
2025-07-31 01:45:57,401 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: SPK-USDT -> 维持保证金率=0.833%
2025-07-31 01:45:57,401 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 1.580秒
2025-07-31 01:45:57,451 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/WIF_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'c4ecb784fbe5763f275809edffb7360a3a36d808f73aa902468bcd9fd7e3af2a95c9f2dbb2e38528d7cfe1b9d6e8355eae4f3bbe02a6ed85b4e3abb3ec1315da', 'Timestamp': '1753897557.4516678', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:57,453 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/CAKE_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '91dcb7dbc3b0a8f5e3be801a54c298af1388638f0318b65751a26bd57258fc548b0eea360ff0373617ebc2bfa008b0b6a46d2e210d92547c4d0aee94f2a3c4be', 'Timestamp': '1753897557.45331', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:57,454 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/ICNT_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'acca3ff244a71e76a6ac0b0fb5e99c0e064379d46f75d2d9733e5d8a1e9c8f40202c962decf80900f8d541b41180bc287dc57272afb6bb5e102528c4bd5daaf5', 'Timestamp': '1753897557.4544325', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:57,456 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/RESOLV_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '2b5790642cceee3b5461f0b51449aebe86da582e8995451b11a36c40683c17f4e2b763c939f19bcb32b45be2049cef22cae1254577207ba92add1671b3464d30', 'Timestamp': '1753897557.4562595', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:45:57,511 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.00005', 'mark_price_round': '0.0001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '5000', 'interest_rate': '0.0003', 'index_price': '2.8062', 'order_price_round': '0.0001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'CAKE_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.15', 'maintenance_rate': '0.006', 'mark_type': 'index', 'funding_interval': 14400, 'type': 'direct', 'risk_limit_step': '4995000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.00005', 'last_price': '2.8035', 'mark_price': '2.8052', 'order_size_max': 10000000, 'funding_next_apply': 1753905600, 'short_users': 172, 'config_change_time': 1753344251, 'create_time': 1615334400, 'trade_size': 2862203530, 'position_size': 4230484, 'long_users': 279, 'quanto_multiplier': '0.1', 'funding_impact_value': '5000', 'leverage_max': '75', 'cross_leverage_default': '10', 'risk_limit_max': '5000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 6572658, 'orderbook_id': 1269757073, 'funding_cap_ratio': '2', 'voucher_leverage': '0', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1615334400}
2025-07-31 01:45:57,512 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: CAKE-USDT -> 维持保证金率=0.600%
2025-07-31 01:45:57,513 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 1.562秒
2025-07-31 01:45:57,514 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.00004', 'mark_price_round': '0.0001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '20000', 'interest_rate': '0.0003', 'index_price': '0.98182', 'order_price_round': '0.0001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'WIF_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.1', 'maintenance_rate': '0.01', 'mark_type': 'index', 'funding_interval': 14400, 'type': 'direct', 'risk_limit_step': '9980000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.00004', 'last_price': '0.9796', 'mark_price': '0.9813', 'order_size_max': 1000000, 'funding_next_apply': 1753905600, 'short_users': 429, 'config_change_time': 1747809959, 'create_time': 1705564563, 'trade_size': 11630566119, 'position_size': 5670340, 'long_users': 1065, 'quanto_multiplier': '1', 'funding_impact_value': '7000', 'leverage_max': '50', 'cross_leverage_default': '10', 'risk_limit_max': '10000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 54601763, 'orderbook_id': 8909731504, 'funding_cap_ratio': '2', 'voucher_leverage': '2', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1705564563}
2025-07-31 01:45:57,515 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: WIF-USDT -> 维持保证金率=1.000%
2025-07-31 01:45:57,516 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 1.567秒
2025-07-31 01:45:57,787 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.00005', 'mark_price_round': '0.0001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '5000', 'interest_rate': '0.0003', 'index_price': '0.19508', 'order_price_round': '0.0001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'RESOLV_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.2', 'maintenance_rate': '0.008333', 'mark_type': 'index', 'funding_interval': 14400, 'type': 'direct', 'risk_limit_step': '9995000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.00005', 'last_price': '0.1949', 'mark_price': '0.195', 'order_size_max': 1000000, 'funding_next_apply': 1753905600, 'short_users': 112, 'config_change_time': 1753276879, 'create_time': 1749560719, 'trade_size': 320053844, 'position_size': 321206, 'long_users': 204, 'quanto_multiplier': '10', 'funding_impact_value': '5000', 'leverage_max': '75', 'cross_leverage_default': '10', 'risk_limit_max': '10000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 2155260, 'orderbook_id': 203807476, 'funding_cap_ratio': '4', 'voucher_leverage': '0', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1749560719}
2025-07-31 01:45:57,788 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: RESOLV-USDT -> 维持保证金率=0.833%
2025-07-31 01:45:57,789 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 1.837秒
2025-07-31 01:45:57,851 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.00005', 'mark_price_round': '0.0001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '5000', 'interest_rate': '0.0003', 'index_price': '0.21618', 'order_price_round': '0.0001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'ICNT_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.2', 'maintenance_rate': '0.01', 'mark_type': 'index', 'funding_interval': 14400, 'type': 'direct', 'risk_limit_step': '2995000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.00005', 'last_price': '0.216', 'mark_price': '0.2161', 'order_size_max': 1000000, 'funding_next_apply': 1753905600, 'short_users': 207, 'config_change_time': 1753883657, 'create_time': 1751510739, 'trade_size': 62071074, 'position_size': 179221, 'long_users': 190, 'quanto_multiplier': '10', 'funding_impact_value': '5000', 'leverage_max': '50', 'cross_leverage_default': '10', 'risk_limit_max': '3000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 1085071, 'orderbook_id': 91770872, 'funding_cap_ratio': '2', 'voucher_leverage': '0', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1751547600}
2025-07-31 01:45:57,852 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: ICNT-USDT -> 维持保证金率=1.000%
2025-07-31 01:45:57,853 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 1.903秒
2025-07-31 01:45:57,853 [INFO] [api_optimizer] ✅ 合约信息第1批完成: 5/5成功
2025-07-31 01:45:59,866 [INFO] [api_optimizer] 🔄 处理合约信息第2/6批
2025-07-31 01:45:59,867 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: AI16Z_USDT
2025-07-31 01:45:59,867 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:45:59,868 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.125秒 (间隔要求: 0.125秒)
2025-07-31 01:45:59,868 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.124秒 (间隔要求: 0.125秒)
2025-07-31 01:45:59,868 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.124秒 (间隔要求: 0.125秒)
2025-07-31 01:45:59,869 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.123秒 (间隔要求: 0.125秒)
2025-07-31 01:46:00,005 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: JUP_USDT
2025-07-31 01:46:00,006 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:00,006 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: MATIC_USDT
2025-07-31 01:46:00,007 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:00,007 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: DOT_USDT
2025-07-31 01:46:00,008 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:00,008 [DEBUG] [exchanges.gate_exchange] Gate.io获取合约信息: SOL_USDT
2025-07-31 01:46:00,009 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:01,381 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/AI16Z_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '541031a1e8f051531dfb9c591937c20451ed45620c282fde3b4a6c11a22e28e4ff8cc0df332c811233bcc98cb20bde4dbe956795a918708682f287c64569d2ef', 'Timestamp': '1753897561.3815641', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:01,439 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.00005', 'mark_price_round': '0.0001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '5000', 'interest_rate': '0.0003', 'index_price': '0.1409', 'order_price_round': '0.0001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'AI16Z_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.15', 'maintenance_rate': '0.006', 'mark_type': 'index', 'funding_interval': 14400, 'type': 'direct', 'risk_limit_step': '4995000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.00005', 'last_price': '0.1407', 'mark_price': '0.1408', 'order_size_max': 1000000, 'funding_next_apply': 1753905600, 'short_users': 270, 'config_change_time': 1753106255, 'create_time': 1734517304, 'trade_size': 1900811022, 'position_size': 2543232, 'long_users': 1067, 'quanto_multiplier': '10', 'funding_impact_value': '7000', 'leverage_max': '75', 'cross_leverage_default': '10', 'risk_limit_max': '5000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 15434714, 'orderbook_id': 1451592095, 'funding_cap_ratio': '2', 'voucher_leverage': '2', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1734517304}
2025-07-31 01:46:01,441 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: AI16Z-USDT -> 维持保证金率=0.600%
2025-07-31 01:46:01,441 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 1.574秒
2025-07-31 01:46:01,506 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/DOT_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '70afa231f0894416e9d387f4191cb00e2f239b875fbfba2991a6a798a6f753bb2fd7e2b2702ed7a120200b97b56bb6adf8d13840559a07b0d5b08554904738c3', 'Timestamp': '1753897561.5066805', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:01,508 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/JUP_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '380b96cac220c0deb41a472b72a0e9e9c68e3b28fec0556bcdcfb3e4f0885b4271e884919e85fd81a2ff7cf4f5f8da2a1fcbc1fceba24a80dc7b99bc3b8ec93f', 'Timestamp': '1753897561.5083964', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:01,510 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/MATIC_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'c949a277e245885375d2556bc4b57cbb24e6294bed5e01863d882e18fd1650d3915fa9059a807d250c0baa85ec3971150b433b4d552347bc6ea3424c35dfa3bc', 'Timestamp': '1753897561.5095809', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:01,511 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/contracts/SOL_USDT?settle=usdt, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '53ac2da95939b0c08f43297c26fe32ed7980a6236b3ff2d5519851860be6efa321624e8d103af2fc6c02cdd27833e9242189526f5b0847130d0af9ad677b3059', 'Timestamp': '1753897561.5113738', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:01,556 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.00005', 'mark_price_round': '0.0001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '1000', 'interest_rate': '0.0003', 'index_price': '0.52513', 'order_price_round': '0.0001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'JUP_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.1', 'maintenance_rate': '0.005', 'mark_type': 'index', 'funding_interval': 14400, 'type': 'direct', 'risk_limit_step': '999000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.00005', 'last_price': '0.525', 'mark_price': '0.525', 'order_size_max': 1000000, 'funding_next_apply': 1753905600, 'short_users': 129, 'config_change_time': 1753427297, 'create_time': 1706717576, 'trade_size': 1698422611, 'position_size': 1489774, 'long_users': 326, 'quanto_multiplier': '1', 'funding_impact_value': '7000', 'leverage_max': '100', 'cross_leverage_default': '10', 'risk_limit_max': '1000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 6623875, 'orderbook_id': 2280208439, 'funding_cap_ratio': '2', 'voucher_leverage': '0', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1706717576}
2025-07-31 01:46:01,558 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: JUP-USDT -> 维持保证金率=0.500%
2025-07-31 01:46:01,558 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 1.554秒
2025-07-31 01:46:01,564 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.0001', 'mark_price_round': '0.001', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '50000', 'interest_rate': '0.0003', 'index_price': '3.8242', 'order_price_round': '0.001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'DOT_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.1', 'maintenance_rate': '0.0066', 'mark_type': 'index', 'funding_interval': 28800, 'type': 'direct', 'risk_limit_step': '19950000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.0001', 'last_price': '3.821', 'mark_price': '3.822', 'order_size_max': 1000000, 'funding_next_apply': 1753920000, 'short_users': 293, 'config_change_time': 1752484604, 'create_time': 1594944000, 'trade_size': 1536158528, 'position_size': 1990017, 'long_users': 963, 'quanto_multiplier': '1', 'funding_impact_value': '10000', 'leverage_max': '75', 'cross_leverage_default': '10', 'risk_limit_max': '20000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 26178945, 'orderbook_id': 11315892369, 'funding_cap_ratio': '0.75', 'voucher_leverage': '0', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1594944000}
2025-07-31 01:46:01,565 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: DOT-USDT -> 维持保证金率=0.660%
2025-07-31 01:46:01,566 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 1.559秒
2025-07-31 01:46:01,569 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.0001', 'mark_price_round': '0.01', 'funding_offset': 0, 'in_delisting': False, 'risk_limit_base': '200000', 'interest_rate': '0.0003', 'index_price': '179.158', 'order_price_round': '0.01', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'SOL_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.1', 'maintenance_rate': '0.005', 'mark_type': 'index', 'funding_interval': 28800, 'type': 'direct', 'risk_limit_step': '199800000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.0001', 'last_price': '178.97', 'mark_price': '179.11', 'order_size_max': 1000000, 'funding_next_apply': 1753920000, 'short_users': 1222, 'config_change_time': 1746773287, 'create_time': 1604880000, 'trade_size': 1131841984, 'position_size': 3435680, 'long_users': 3940, 'quanto_multiplier': '1', 'funding_impact_value': '10000', 'leverage_max': '100', 'cross_leverage_default': '10', 'risk_limit_max': '200000000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 101798755, 'orderbook_id': 31258835820, 'funding_cap_ratio': '0.75', 'voucher_leverage': '2', 'is_pre_market': False, 'status': 'trading', 'launch_time': 1604880000}
2025-07-31 01:46:01,570 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: SOL-USDT -> 维持保证金率=0.500%
2025-07-31 01:46:01,571 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 1.563秒
2025-07-31 01:46:01,572 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'funding_rate_indicative': '0.0001', 'mark_price_round': '0.0001', 'funding_offset': 0, 'in_delisting': True, 'risk_limit_base': '1000', 'interest_rate': '0.0003', 'index_price': '0.423325', 'order_price_round': '0.0001', 'order_size_min': 1, 'ref_rebate_rate': '0.2', 'name': 'MATIC_USDT', 'ref_discount_rate': '0', 'order_price_deviate': '0.5', 'maintenance_rate': '0.08', 'mark_type': 'index', 'funding_interval': 28800, 'type': 'direct', 'risk_limit_step': '49000', 'enable_bonus': True, 'enable_credit': True, 'leverage_min': '1', 'funding_rate': '0.0001', 'last_price': '0.4229', 'mark_price': '0.4233', 'order_size_max': 1000000, 'funding_next_apply': 1753920000, 'short_users': 0, 'config_change_time': 1716966847, 'create_time': 1615420800, 'trade_size': 682237402, 'position_size': 0, 'long_users': 0, 'quanto_multiplier': '10', 'funding_impact_value': '5000', 'leverage_max': '10', 'cross_leverage_default': '10', 'risk_limit_max': '50000', 'maker_fee_rate': '-0.0001', 'taker_fee_rate': '0.00075', 'orders_limit': 100, 'trade_id': 17730201, 'orderbook_id': 11348322731, 'funding_cap_ratio': '0.75', 'voucher_leverage': '2', 'is_pre_market': False, 'status': 'delisted', 'launch_time': 1615420800}
2025-07-31 01:46:01,573 [INFO] [exchanges.gate_exchange] ✅ Gate.io合约信息获取成功: MATIC-USDT -> 维持保证金率=8.000%
2025-07-31 01:46:01,574 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 1.568秒
2025-07-31 01:46:01,574 [INFO] [api_optimizer] ✅ 合约信息第2批完成: 5/5成功
2025-07-31 01:46:03,571 [INFO] [api_optimizer] 🔄 处理合约信息第3/6批
2025-07-31 01:46:03,573 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: SPKUSDT
2025-07-31 01:46:03,573 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:03,573 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:46:03,573 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:46:03,575 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.247秒 (间隔要求: 0.250秒)
2025-07-31 01:46:03,575 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.247秒 (间隔要求: 0.250秒)
2025-07-31 01:46:03,818 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: CAKEUSDT
2025-07-31 01:46:03,818 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:46:03,819 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: RESOLVUSDT
2025-07-31 01:46:03,819 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:46:03,820 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: ICNTUSDT
2025-07-31 01:46:03,821 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:03,821 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: WIFUSDT
2025-07-31 01:46:03,822 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:05,077 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:46:05,078 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT
2025-07-31 01:46:05,078 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:46:05,079 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'SPKUSDT'}
2025-07-31 01:46:05,323 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:46:05,323 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=WIFUSDT
2025-07-31 01:46:05,324 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:46:05,324 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'WIFUSDT'}
2025-07-31 01:46:05,326 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:46:05,327 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT
2025-07-31 01:46:05,327 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:46:05,328 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'ICNTUSDT'}
2025-07-31 01:46:05,329 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:46:05,330 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT
2025-07-31 01:46:05,330 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:46:05,331 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'RESOLVUSDT'}
2025-07-31 01:46:05,332 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:46:05,333 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT
2025-07-31 01:46:05,333 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:46:05,333 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'CAKEUSDT'}
2025-07-31 01:46:05,377 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: SPK-USDT -> 最大杠杆=25.0x
2025-07-31 01:46:05,377 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 1.805秒
2025-07-31 01:46:06,100 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: WIF-USDT -> 最大杠杆=75.0x
2025-07-31 01:46:06,101 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.280秒
2025-07-31 01:46:06,360 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: RESOLV-USDT -> 最大杠杆=25.0x
2025-07-31 01:46:06,361 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.542秒
2025-07-31 01:46:06,384 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: ICNT-USDT -> 最大杠杆=20.0x
2025-07-31 01:46:06,384 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.564秒
2025-07-31 01:46:06,393 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: CAKE-USDT -> 最大杠杆=25.0x
2025-07-31 01:46:06,394 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.576秒
2025-07-31 01:46:06,395 [INFO] [api_optimizer] ✅ 合约信息第3批完成: 5/5成功
2025-07-31 01:46:08,403 [INFO] [api_optimizer] 🔄 处理合约信息第4/6批
2025-07-31 01:46:08,404 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: AI16ZUSDT
2025-07-31 01:46:08,405 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:08,406 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.249秒 (间隔要求: 0.250秒)
2025-07-31 01:46:08,406 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:46:08,407 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:46:08,407 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:46:08,670 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: DOTUSDT
2025-07-31 01:46:08,670 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:46:08,671 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: JUPUSDT
2025-07-31 01:46:08,671 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:46:08,672 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: MATICUSDT
2025-07-31 01:46:08,672 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:46:08,673 [DEBUG] [exchanges.bybit_exchange] Bybit获取合约信息: SOLUSDT
2025-07-31 01:46:08,673 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:09,928 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:46:09,929 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=AI16ZUSDT
2025-07-31 01:46:09,929 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:46:09,929 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'AI16ZUSDT'}
2025-07-31 01:46:10,191 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:46:10,192 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SOLUSDT
2025-07-31 01:46:10,192 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:46:10,193 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'SOLUSDT'}
2025-07-31 01:46:10,195 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:46:10,195 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=DOTUSDT
2025-07-31 01:46:10,196 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:46:10,196 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'DOTUSDT'}
2025-07-31 01:46:10,197 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:46:10,198 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=JUPUSDT
2025-07-31 01:46:10,198 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:46:10,199 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'JUPUSDT'}
2025-07-31 01:46:10,200 [DEBUG] [bybit_exchange] 🔧 Bybit参数修复: 2 → 2 参数
2025-07-31 01:46:10,201 [DEBUG] [exchanges.bybit_exchange] Bybit请求: GET https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=MATICUSDT
2025-07-31 01:46:10,201 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-31 01:46:10,202 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'MATICUSDT'}
2025-07-31 01:46:10,227 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: AI16Z-USDT -> 最大杠杆=75.0x
2025-07-31 01:46:10,227 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 1.823秒
2025-07-31 01:46:10,488 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: DOT-USDT -> 最大杠杆=50.0x
2025-07-31 01:46:10,489 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 1.819秒
2025-07-31 01:46:10,491 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: SOL-USDT -> 最大杠杆=100.0x
2025-07-31 01:46:10,492 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 1.819秒
2025-07-31 01:46:10,501 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: JUP-USDT -> 最大杠杆=50.0x
2025-07-31 01:46:10,502 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 1.831秒
2025-07-31 01:46:10,503 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: MATIC-USDT -> 最大杠杆=75.0x
2025-07-31 01:46:10,504 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 1.832秒
2025-07-31 01:46:10,505 [INFO] [api_optimizer] ✅ 合约信息第4批完成: 5/5成功
2025-07-31 01:46:12,522 [INFO] [api_optimizer] 🔄 处理合约信息第5/6批
2025-07-31 01:46:12,523 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: SPK-USDT-SWAP
2025-07-31 01:46:12,523 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.500秒 (要求: 0.500秒)
2025-07-31 01:46:12,524 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.499秒 (间隔要求: 0.500秒)
2025-07-31 01:46:12,524 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.499秒 (间隔要求: 0.500秒)
2025-07-31 01:46:12,525 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.498秒 (间隔要求: 0.500秒)
2025-07-31 01:46:12,526 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.497秒 (间隔要求: 0.500秒)
2025-07-31 01:46:13,020 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: WIF-USDT-SWAP
2025-07-31 01:46:13,020 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.500秒 (要求: 0.500秒)
2025-07-31 01:46:13,021 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: CAKE-USDT-SWAP
2025-07-31 01:46:13,021 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.500秒 (要求: 0.500秒)
2025-07-31 01:46:13,021 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: RESOLV-USDT-SWAP
2025-07-31 01:46:13,022 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.499秒 (要求: 0.500秒)
2025-07-31 01:46:13,022 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: ICNT-USDT-SWAP
2025-07-31 01:46:13,022 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.500秒 (要求: 0.500秒)
2025-07-31 01:46:13,023 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:46:13,023 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:17.402Z
2025-07-31 01:46:13,023 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'SPK-USDT-SWAP'}
2025-07-31 01:46:13,131 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'alias': '', 'auctionEndTime': '', 'baseCcy': '', 'category': '1', 'contTdSwTime': '', 'ctMult': '1', 'ctType': 'linear', 'ctVal': '100', 'ctValCcy': 'SPK', 'expTime': '', 'futureSettlement': False, 'instFamily': 'SPK-USDT', 'instId': 'SPK-USDT-SWAP', 'instIdCode': 214785, 'instType': 'SWAP', 'lever': '20', 'listTime': '1750156200346', 'lotSz': '1', 'maxIcebergSz': '100000000.0000000000000000', 'maxLmtAmt': '20000000', 'maxLmtSz': '100000000', 'maxMktAmt': '', 'maxMktSz': '20000', 'maxStopSz': '20000', 'maxTriggerSz': '100000000.0000000000000000', 'maxTwapSz': '100000000.0000000000000000', 'minSz': '1', 'openType': '', 'optType': '', 'quoteCcy': '', 'ruleType': 'normal', 'settleCcy': 'USDT', 'state': 'live', 'stk': '', 'tickSz': '0.00001', 'tradeQuoteCcyList': [], 'uly': 'SPK-USDT'}], 'msg': ''}
2025-07-31 01:46:13,133 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-31 01:46:13,133 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.610秒
2025-07-31 01:46:13,529 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:46:13,530 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:17.909Z
2025-07-31 01:46:13,530 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'RESOLV-USDT-SWAP'}
2025-07-31 01:46:13,532 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:46:13,533 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:17.912Z
2025-07-31 01:46:13,533 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'CAKE-USDT-SWAP'}
2025-07-31 01:46:13,535 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:46:13,535 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:17.914Z
2025-07-31 01:46:13,536 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'ICNT-USDT-SWAP'}
2025-07-31 01:46:13,537 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:46:13,538 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:17.917Z
2025-07-31 01:46:13,538 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'WIF-USDT-SWAP'}
2025-07-31 01:46:13,655 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'alias': '', 'auctionEndTime': '', 'baseCcy': '', 'category': '1', 'contTdSwTime': '', 'ctMult': '1', 'ctType': 'linear', 'ctVal': '10', 'ctValCcy': 'RESOLV', 'expTime': '', 'futureSettlement': False, 'instFamily': 'RESOLV-USDT', 'instId': 'RESOLV-USDT-SWAP', 'instIdCode': 213805, 'instType': 'SWAP', 'lever': '50', 'listTime': '1749565800438', 'lotSz': '1', 'maxIcebergSz': '100000000.0000000000000000', 'maxLmtAmt': '20000000', 'maxLmtSz': '100000000', 'maxMktAmt': '', 'maxMktSz': '50000', 'maxStopSz': '50000', 'maxTriggerSz': '100000000.0000000000000000', 'maxTwapSz': '100000000.0000000000000000', 'minSz': '1', 'openType': '', 'optType': '', 'quoteCcy': '', 'ruleType': 'normal', 'settleCcy': 'USDT', 'state': 'live', 'stk': '', 'tickSz': '0.0001', 'tradeQuoteCcyList': [], 'uly': 'RESOLV-USDT'}], 'msg': ''}
2025-07-31 01:46:13,656 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 01:46:13,657 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.636秒
2025-07-31 01:46:13,938 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:46:13,938 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:46:13,939 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:46:13,939 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:46:13,940 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/public/instruments
2025-07-31 01:46:13,940 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:46:13,941 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 01:46:13,941 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.919秒
2025-07-31 01:46:13,951 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:46:13,951 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:46:13,952 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:46:13,952 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:46:13,952 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/public/instruments
2025-07-31 01:46:13,952 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:46:13,953 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 01:46:13,953 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.932秒
2025-07-31 01:46:13,956 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'alias': '', 'auctionEndTime': '', 'baseCcy': '', 'category': '1', 'contTdSwTime': '', 'ctMult': '1', 'ctType': 'linear', 'ctVal': '1', 'ctValCcy': 'WIF', 'expTime': '', 'futureSettlement': False, 'instFamily': 'WIF-USDT', 'instId': 'WIF-USDT-SWAP', 'instIdCode': 155700, 'instType': 'SWAP', 'lever': '20', 'listTime': '1713173100603', 'lotSz': '1', 'maxIcebergSz': '100000000.0000000000000000', 'maxLmtAmt': '20000000', 'maxLmtSz': '100000000', 'maxMktAmt': '', 'maxMktSz': '50000', 'maxStopSz': '50000', 'maxTriggerSz': '100000000.0000000000000000', 'maxTwapSz': '100000000.0000000000000000', 'minSz': '1', 'openType': '', 'optType': '', 'quoteCcy': '', 'ruleType': 'normal', 'settleCcy': 'USDT', 'state': 'live', 'stk': '', 'tickSz': '0.0001', 'tradeQuoteCcyList': [], 'uly': 'WIF-USDT'}], 'msg': ''}
2025-07-31 01:46:13,957 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-07-31 01:46:13,957 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.937秒
2025-07-31 01:46:13,957 [INFO] [api_optimizer] ✅ 合约信息第5批完成: 5/5成功
2025-07-31 01:46:15,968 [INFO] [api_optimizer] 🔄 处理合约信息第6/6批
2025-07-31 01:46:15,968 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: AI16Z-USDT-SWAP
2025-07-31 01:46:15,969 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.499秒 (要求: 0.500秒)
2025-07-31 01:46:15,969 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.499秒 (间隔要求: 0.500秒)
2025-07-31 01:46:15,970 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.498秒 (间隔要求: 0.500秒)
2025-07-31 01:46:15,970 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.498秒 (间隔要求: 0.500秒)
2025-07-31 01:46:15,971 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.498秒 (间隔要求: 0.500秒)
2025-07-31 01:46:16,478 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: MATIC-USDT-SWAP
2025-07-31 01:46:16,479 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.500秒 (要求: 0.500秒)
2025-07-31 01:46:16,479 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: DOT-USDT-SWAP
2025-07-31 01:46:16,480 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.499秒 (要求: 0.500秒)
2025-07-31 01:46:16,480 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: JUP-USDT-SWAP
2025-07-31 01:46:16,481 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.499秒 (要求: 0.500秒)
2025-07-31 01:46:16,481 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:46:16,482 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:20.861Z
2025-07-31 01:46:16,482 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'AI16Z-USDT-SWAP'}
2025-07-31 01:46:16,485 [DEBUG] [exchanges.okx_exchange] OKX获取合约信息: SOL-USDT-SWAP
2025-07-31 01:46:16,485 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.499秒 (要求: 0.500秒)
2025-07-31 01:46:16,603 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'alias': '', 'auctionEndTime': '', 'baseCcy': '', 'category': '1', 'contTdSwTime': '', 'ctMult': '1', 'ctType': 'linear', 'ctVal': '1', 'ctValCcy': 'AI16Z', 'expTime': '', 'futureSettlement': False, 'instFamily': 'AI16Z-USDT', 'instId': 'AI16Z-USDT-SWAP', 'instIdCode': 189539, 'instType': 'SWAP', 'lever': '20', 'listTime': '1734686100190', 'lotSz': '1', 'maxIcebergSz': '100000000.0000000000000000', 'maxLmtAmt': '20000000', 'maxLmtSz': '100000000', 'maxMktAmt': '', 'maxMktSz': '100000', 'maxStopSz': '100000', 'maxTriggerSz': '100000000.0000000000000000', 'maxTwapSz': '100000000.0000000000000000', 'minSz': '1', 'openType': '', 'optType': '', 'quoteCcy': '', 'ruleType': 'normal', 'settleCcy': 'USDT', 'state': 'live', 'stk': '', 'tickSz': '0.0001', 'tradeQuoteCcyList': [], 'uly': 'AI16Z-USDT'}], 'msg': ''}
2025-07-31 01:46:16,604 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-07-31 01:46:16,605 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.637秒
2025-07-31 01:46:16,987 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:46:16,987 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:21.366Z
2025-07-31 01:46:16,988 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'DOT-USDT-SWAP'}
2025-07-31 01:46:16,989 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:46:16,989 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:21.369Z
2025-07-31 01:46:16,990 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'JUP-USDT-SWAP'}
2025-07-31 01:46:16,991 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:46:16,991 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:21.370Z
2025-07-31 01:46:16,991 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'MATIC-USDT-SWAP'}
2025-07-31 01:46:16,992 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/public/instruments
2025-07-31 01:46:16,993 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:21.372Z
2025-07-31 01:46:16,993 [DEBUG] [exchanges.okx_exchange] OKX请求参数: {'instType': 'SWAP', 'instId': 'SOL-USDT-SWAP'}
2025-07-31 01:46:17,105 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'alias': '', 'auctionEndTime': '', 'baseCcy': '', 'category': '1', 'contTdSwTime': '', 'ctMult': '1', 'ctType': 'linear', 'ctVal': '1', 'ctValCcy': 'DOT', 'expTime': '', 'futureSettlement': False, 'instFamily': 'DOT-USDT', 'instId': 'DOT-USDT-SWAP', 'instIdCode': 10504, 'instType': 'SWAP', 'lever': '50', 'listTime': '1598350371000', 'lotSz': '1', 'maxIcebergSz': '100000000.0000000000000000', 'maxLmtAmt': '20000000', 'maxLmtSz': '100000000', 'maxMktAmt': '', 'maxMktSz': '70000', 'maxStopSz': '70000', 'maxTriggerSz': '100000000.0000000000000000', 'maxTwapSz': '100000000.0000000000000000', 'minSz': '1', 'openType': '', 'optType': '', 'quoteCcy': '', 'ruleType': 'normal', 'settleCcy': 'USDT', 'state': 'live', 'stk': '', 'tickSz': '0.001', 'tradeQuoteCcyList': [], 'uly': 'DOT-USDT'}], 'msg': ''}
2025-07-31 01:46:17,106 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-07-31 01:46:17,107 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.628秒
2025-07-31 01:46:17,108 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:46:17,109 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:46:17,110 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:46:17,110 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:46:17,110 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/public/instruments
2025-07-31 01:46:17,111 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:46:17,111 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-07-31 01:46:17,112 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.634秒
2025-07-31 01:46:17,113 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'alias': '', 'auctionEndTime': '', 'baseCcy': '', 'category': '1', 'contTdSwTime': '', 'ctMult': '1', 'ctType': 'linear', 'ctVal': '1', 'ctValCcy': 'SOL', 'expTime': '', 'futureSettlement': False, 'instFamily': 'SOL-USDT', 'instId': 'SOL-USDT-SWAP', 'instIdCode': 10530, 'instType': 'SWAP', 'lever': '50', 'listTime': '1611298800000', 'lotSz': '0.01', 'maxIcebergSz': '100000000.0000000000000000', 'maxLmtAmt': '20000000', 'maxLmtSz': '100000000', 'maxMktAmt': '', 'maxMktSz': '15000', 'maxStopSz': '15000', 'maxTriggerSz': '100000000.0000000000000000', 'maxTwapSz': '100000000.0000000000000000', 'minSz': '0.01', 'openType': '', 'optType': '', 'quoteCcy': '', 'ruleType': 'normal', 'settleCcy': 'USDT', 'state': 'live', 'stk': '', 'tickSz': '0.01', 'tradeQuoteCcyList': [], 'uly': 'SOL-USDT'}], 'msg': ''}
2025-07-31 01:46:17,114 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-07-31 01:46:17,115 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.630秒
2025-07-31 01:46:17,116 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'alias': '', 'auctionEndTime': '', 'baseCcy': '', 'category': '1', 'contTdSwTime': '', 'ctMult': '1', 'ctType': 'linear', 'ctVal': '10', 'ctValCcy': 'JUP', 'expTime': '', 'futureSettlement': False, 'instFamily': 'JUP-USDT', 'instId': 'JUP-USDT-SWAP', 'instIdCode': 146691, 'instType': 'SWAP', 'lever': '50', 'listTime': '1706717100211', 'lotSz': '1', 'maxIcebergSz': '100000000.0000000000000000', 'maxLmtAmt': '20000000', 'maxLmtSz': '100000000', 'maxMktAmt': '', 'maxMktSz': '20000', 'maxStopSz': '20000', 'maxTriggerSz': '100000000.0000000000000000', 'maxTwapSz': '100000000.0000000000000000', 'minSz': '1', 'openType': '', 'optType': '', 'quoteCcy': '', 'ruleType': 'normal', 'settleCcy': 'USDT', 'state': 'live', 'stk': '', 'tickSz': '0.0001', 'tradeQuoteCcyList': [], 'uly': 'JUP-USDT'}], 'msg': ''}
2025-07-31 01:46:17,117 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-07-31 01:46:17,118 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.638秒
2025-07-31 01:46:17,118 [INFO] [api_optimizer] ✅ 合约信息第6批完成: 5/5成功
2025-07-31 01:46:17,119 [INFO] [api_optimizer] ✅ 分批获取合约信息完成
2025-07-31 01:46:17,119 [INFO] [api_optimizer] 🔧 延迟杠杆设置...
2025-07-31 01:46:18,129 [INFO] [exchanges.gate_exchange] 🔄 Gate.io杠杆设置重定向到统一杠杆管理器: SPK-USDT
2025-07-31 01:46:18,130 [DEBUG] [core.unified_leverage_manager] 🎯 杠杆缓存命中: gate SPK-USDT -> 3x
2025-07-31 01:46:18,131 [INFO] [core.unified_leverage_manager] 🎯 杠杆缓存命中，跳过设置: gate SPK-USDT 3x
2025-07-31 01:46:18,131 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.002秒
2025-07-31 01:46:18,132 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.122秒 (间隔要求: 0.125秒)
2025-07-31 01:46:18,132 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.122秒 (间隔要求: 0.125秒)
2025-07-31 01:46:18,133 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.121秒 (间隔要求: 0.125秒)
2025-07-31 01:46:18,133 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.121秒 (间隔要求: 0.125秒)
2025-07-31 01:46:18,134 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.120秒 (间隔要求: 0.125秒)
2025-07-31 01:46:18,134 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.120秒 (间隔要求: 0.125秒)
2025-07-31 01:46:18,135 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.119秒 (间隔要求: 0.125秒)
2025-07-31 01:46:18,135 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.119秒 (间隔要求: 0.125秒)
2025-07-31 01:46:18,136 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.118秒 (间隔要求: 0.125秒)
2025-07-31 01:46:18,137 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆: SPK-USDT -> SPKUSDT 3倍（系统限制最大3.0倍）
2025-07-31 01:46:18,137 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:46:18,138 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.249秒 (间隔要求: 0.250秒)
2025-07-31 01:46:18,138 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.249秒 (间隔要求: 0.250秒)
2025-07-31 01:46:18,139 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:46:18,140 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.248秒 (间隔要求: 0.250秒)
2025-07-31 01:46:18,140 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.247秒 (间隔要求: 0.250秒)
2025-07-31 01:46:18,141 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.246秒 (间隔要求: 0.250秒)
2025-07-31 01:46:18,141 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.246秒 (间隔要求: 0.250秒)
2025-07-31 01:46:18,142 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.245秒 (间隔要求: 0.250秒)
2025-07-31 01:46:18,142 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.245秒 (间隔要求: 0.250秒)
2025-07-31 01:46:18,143 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:46:18,143 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.500秒 (要求: 0.500秒)
2025-07-31 01:46:18,144 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.499秒 (间隔要求: 0.500秒)
2025-07-31 01:46:18,145 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.498秒 (间隔要求: 0.500秒)
2025-07-31 01:46:18,146 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.497秒 (间隔要求: 0.500秒)
2025-07-31 01:46:18,146 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.497秒 (间隔要求: 0.500秒)
2025-07-31 01:46:18,147 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.496秒 (间隔要求: 0.500秒)
2025-07-31 01:46:18,147 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.496秒 (间隔要求: 0.500秒)
2025-07-31 01:46:18,148 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.495秒 (间隔要求: 0.500秒)
2025-07-31 01:46:18,149 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.494秒 (间隔要求: 0.500秒)
2025-07-31 01:46:18,149 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.494秒 (间隔要求: 0.500秒)
2025-07-31 01:46:18,268 [INFO] [exchanges.gate_exchange] 🔄 Gate.io杠杆设置重定向到统一杠杆管理器: JUP-USDT
2025-07-31 01:46:18,268 [INFO] [core.unified_leverage_manager] 🔧 Gate统一杠杆设置: JUP-USDT 3倍
2025-07-31 01:46:18,269 [INFO] [core.unified_leverage_manager] 🔧 Gate.io杠杆设置: 合约=JUP_USDT, 杠杆=3倍
2025-07-31 01:46:18,269 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:18,270 [INFO] [exchanges.gate_exchange] 🔄 Gate.io杠杆设置重定向到统一杠杆管理器: MATIC-USDT
2025-07-31 01:46:18,270 [INFO] [core.unified_leverage_manager] 🔧 Gate统一杠杆设置: MATIC-USDT 3倍
2025-07-31 01:46:18,271 [INFO] [core.unified_leverage_manager] 🔧 Gate.io杠杆设置: 合约=MATIC_USDT, 杠杆=3倍
2025-07-31 01:46:18,272 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.498秒 (要求: 1.500秒)
2025-07-31 01:46:18,272 [INFO] [exchanges.gate_exchange] 🔄 Gate.io杠杆设置重定向到统一杠杆管理器: DOT-USDT
2025-07-31 01:46:18,273 [INFO] [core.unified_leverage_manager] 🔧 Gate统一杠杆设置: DOT-USDT 3倍
2025-07-31 01:46:18,273 [INFO] [core.unified_leverage_manager] 🔧 Gate.io杠杆设置: 合约=DOT_USDT, 杠杆=3倍
2025-07-31 01:46:18,274 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.498秒 (要求: 1.500秒)
2025-07-31 01:46:18,274 [INFO] [exchanges.gate_exchange] 🔄 Gate.io杠杆设置重定向到统一杠杆管理器: AI16Z-USDT
2025-07-31 01:46:18,275 [DEBUG] [core.unified_leverage_manager] 🎯 杠杆缓存命中: gate AI16Z-USDT -> 3x
2025-07-31 01:46:18,275 [INFO] [core.unified_leverage_manager] 🎯 杠杆缓存命中，跳过设置: gate AI16Z-USDT 3x
2025-07-31 01:46:18,276 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.002秒
2025-07-31 01:46:18,277 [INFO] [exchanges.gate_exchange] 🔄 Gate.io杠杆设置重定向到统一杠杆管理器: SOL-USDT
2025-07-31 01:46:18,277 [INFO] [core.unified_leverage_manager] 🔧 Gate统一杠杆设置: SOL-USDT 3倍
2025-07-31 01:46:18,278 [INFO] [core.unified_leverage_manager] 🔧 Gate.io杠杆设置: 合约=SOL_USDT, 杠杆=3倍
2025-07-31 01:46:18,278 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.498秒 (要求: 1.500秒)
2025-07-31 01:46:18,279 [INFO] [exchanges.gate_exchange] 🔄 Gate.io杠杆设置重定向到统一杠杆管理器: CAKE-USDT
2025-07-31 01:46:18,279 [DEBUG] [core.unified_leverage_manager] 🎯 杠杆缓存命中: gate CAKE-USDT -> 3x
2025-07-31 01:46:18,280 [INFO] [core.unified_leverage_manager] 🎯 杠杆缓存命中，跳过设置: gate CAKE-USDT 3x
2025-07-31 01:46:18,280 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.001秒
2025-07-31 01:46:18,280 [INFO] [exchanges.gate_exchange] 🔄 Gate.io杠杆设置重定向到统一杠杆管理器: WIF-USDT
2025-07-31 01:46:18,281 [DEBUG] [core.unified_leverage_manager] 🎯 杠杆缓存命中: gate WIF-USDT -> 3x
2025-07-31 01:46:18,281 [INFO] [core.unified_leverage_manager] 🎯 杠杆缓存命中，跳过设置: gate WIF-USDT 3x
2025-07-31 01:46:18,282 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.002秒
2025-07-31 01:46:18,282 [INFO] [exchanges.gate_exchange] 🔄 Gate.io杠杆设置重定向到统一杠杆管理器: RESOLV-USDT
2025-07-31 01:46:18,283 [DEBUG] [core.unified_leverage_manager] 🎯 杠杆缓存命中: gate RESOLV-USDT -> 3x
2025-07-31 01:46:18,284 [INFO] [core.unified_leverage_manager] 🎯 杠杆缓存命中，跳过设置: gate RESOLV-USDT 3x
2025-07-31 01:46:18,284 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.002秒
2025-07-31 01:46:18,284 [INFO] [exchanges.gate_exchange] 🔄 Gate.io杠杆设置重定向到统一杠杆管理器: ICNT-USDT
2025-07-31 01:46:18,285 [DEBUG] [core.unified_leverage_manager] 🎯 杠杆缓存命中: gate ICNT-USDT -> 3x
2025-07-31 01:46:18,286 [INFO] [core.unified_leverage_manager] 🎯 杠杆缓存命中，跳过设置: gate ICNT-USDT 3x
2025-07-31 01:46:18,286 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.002秒
2025-07-31 01:46:18,393 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆: DOT-USDT -> DOTUSDT 3倍（系统限制最大3.0倍）
2025-07-31 01:46:18,393 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:18,394 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆: JUP-USDT -> JUPUSDT 3倍（系统限制最大3.0倍）
2025-07-31 01:46:18,394 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:46:18,395 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆: SOL-USDT -> SOLUSDT 3倍（系统限制最大3.0倍）
2025-07-31 01:46:18,395 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:46:18,396 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆: MATIC-USDT -> MATICUSDT 3倍（系统限制最大3.0倍）
2025-07-31 01:46:18,396 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:46:18,397 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆: AI16Z-USDT -> AI16ZUSDT 3倍（系统限制最大3.0倍）
2025-07-31 01:46:18,397 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:46:18,398 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆: CAKE-USDT -> CAKEUSDT 3倍（系统限制最大3.0倍）
2025-07-31 01:46:18,398 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.500秒 (要求: 1.500秒)
2025-07-31 01:46:18,399 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆: WIF-USDT -> WIFUSDT 3倍（系统限制最大3.0倍）
2025-07-31 01:46:18,400 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:18,400 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆: RESOLV-USDT -> RESOLVUSDT 3倍（系统限制最大3.0倍）
2025-07-31 01:46:18,401 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:18,402 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆: ICNT-USDT -> ICNTUSDT 3倍（系统限制最大3.0倍）
2025-07-31 01:46:18,402 [INFO] [api_optimizer] 🕐 bybit 健壮冷却等待: 1.499秒 (要求: 1.500秒)
2025-07-31 01:46:18,657 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:46:18,658 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.499秒 (要求: 0.500秒)
2025-07-31 01:46:18,658 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:46:18,659 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.499秒 (要求: 0.500秒)
2025-07-31 01:46:18,659 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:46:18,660 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.499秒 (要求: 0.500秒)
2025-07-31 01:46:18,660 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:46:18,661 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.499秒 (要求: 0.500秒)
2025-07-31 01:46:18,661 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:46:18,662 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.499秒 (要求: 0.500秒)
2025-07-31 01:46:18,663 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:46:18,663 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.499秒 (要求: 0.500秒)
2025-07-31 01:46:18,664 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:46:18,664 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.500秒 (要求: 0.500秒)
2025-07-31 01:46:18,665 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:46:18,665 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.500秒 (要求: 0.500秒)
2025-07-31 01:46:18,666 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 01:46:18,666 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.500秒 (要求: 0.500秒)
2025-07-31 01:46:18,667 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:46:18,668 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:23.047Z
2025-07-31 01:46:18,668 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:46:18,795 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:46:18,796 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:46:18,797 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:46:18,797 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.370秒 (要求: 0.500秒)
2025-07-31 01:46:19,169 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:46:19,170 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:23.549Z
2025-07-31 01:46:19,170 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:46:19,172 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:46:19,173 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:23.552Z
2025-07-31 01:46:19,174 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:46:19,175 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:46:19,175 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:23.555Z
2025-07-31 01:46:19,176 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:46:19,177 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:46:19,178 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:23.557Z
2025-07-31 01:46:19,178 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:46:19,180 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:46:19,180 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:23.559Z
2025-07-31 01:46:19,180 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:46:19,182 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:46:19,183 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:23.561Z
2025-07-31 01:46:19,183 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:46:19,184 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:46:19,185 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:23.564Z
2025-07-31 01:46:19,186 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:46:19,187 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:46:19,187 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:23.567Z
2025-07-31 01:46:19,187 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:46:19,189 [DEBUG] [exchanges.okx_exchange] OKX请求: GET https://www.okx.com/api/v5/account/config
2025-07-31 01:46:19,190 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:23.569Z
2025-07-31 01:46:19,190 [DEBUG] [exchanges.okx_exchange] OKX请求参数: None
2025-07-31 01:46:19,191 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:46:19,192 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:23.571Z
2025-07-31 01:46:19,192 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "SPK-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:46:19,292 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:46:19,293 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:46:19,294 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:46:19,295 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.397秒 (要求: 0.500秒)
2025-07-31 01:46:19,303 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:46:19,303 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:46:19,304 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:46:19,305 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.387秒 (要求: 0.500秒)
2025-07-31 01:46:19,306 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:46:19,307 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:46:19,308 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:46:19,308 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.383秒 (要求: 0.500秒)
2025-07-31 01:46:19,314 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}], 'msg': ''}
2025-07-31 01:46:19,315 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 01:46:19,316 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:46:19,316 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.375秒 (要求: 0.500秒)
2025-07-31 01:46:19,577 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'SPK-USDT-SWAP', 'lever': '3', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:46:19,578 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:46:19,579 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:46:19,579 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 1.436秒
2025-07-31 01:46:19,595 [DEBUG] [exchanges.okx_exchange] OKX响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 01:46:19,595 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 01:46:19,596 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 01:46:19,596 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 01:46:19,597 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 01:46:19,598 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 01:46:19,598 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 01:46:19,598 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 01:46:19,599 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 01:46:19,599 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 01:46:19,600 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:46:19,600 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.091秒 (要求: 0.500秒)
2025-07-31 01:46:19,602 [DEBUG] [exchanges.okx_exchange] OKX响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 01:46:19,602 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 01:46:19,603 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 01:46:19,603 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 01:46:19,604 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 01:46:19,604 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 01:46:19,605 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 01:46:19,605 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 01:46:19,606 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 01:46:19,606 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 01:46:19,606 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:46:19,607 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.084秒 (要求: 0.500秒)
2025-07-31 01:46:19,609 [DEBUG] [exchanges.okx_exchange] OKX响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 01:46:19,609 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 01:46:19,609 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 01:46:19,610 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 01:46:19,610 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 01:46:19,611 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 01:46:19,611 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 01:46:19,612 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 01:46:19,612 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 01:46:19,613 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 01:46:19,614 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:46:19,614 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.077秒 (要求: 0.500秒)
2025-07-31 01:46:19,615 [DEBUG] [exchanges.okx_exchange] OKX响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 01:46:19,615 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 01:46:19,616 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 01:46:19,616 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 01:46:19,617 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 01:46:19,617 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 01:46:19,618 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 01:46:19,618 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 01:46:19,619 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 01:46:19,619 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 01:46:19,620 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:46:19,620 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.072秒 (要求: 0.500秒)
2025-07-31 01:46:19,623 [DEBUG] [exchanges.okx_exchange] OKX响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 01:46:19,624 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 01:46:19,625 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 01:46:19,625 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 01:46:19,625 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 01:46:19,626 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 01:46:19,626 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 01:46:19,627 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 01:46:19,627 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 01:46:19,628 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 01:46:19,628 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 01:46:19,628 [INFO] [api_optimizer] 🕐 okx 健壮冷却等待: 0.063秒 (要求: 0.500秒)
2025-07-31 01:46:19,629 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:46:19,630 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'SPKUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:19,699 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:46:19,700 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:24.079Z
2025-07-31 01:46:19,700 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "DOT-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:46:19,702 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:46:19,702 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:24.081Z
2025-07-31 01:46:19,702 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "MATIC-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:46:19,704 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:46:19,704 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:24.083Z
2025-07-31 01:46:19,704 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "CAKE-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:46:19,705 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:46:19,705 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:24.084Z
2025-07-31 01:46:19,705 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "RESOLV-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:46:19,706 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:46:19,706 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:24.086Z
2025-07-31 01:46:19,707 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "WIF-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:46:19,707 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:46:19,708 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:24.087Z
2025-07-31 01:46:19,708 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "SOL-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:46:19,709 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:46:19,710 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:24.089Z
2025-07-31 01:46:19,710 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "JUP-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:46:19,711 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:46:19,711 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:24.091Z
2025-07-31 01:46:19,711 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "ICNT-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:46:19,713 [DEBUG] [exchanges.okx_exchange] OKX请求: POST https://www.okx.com/api/v5/account/set-leverage
2025-07-31 01:46:19,713 [DEBUG] [exchanges.okx_exchange] OKX时间戳: 2025-07-30T17:46:24.092Z
2025-07-31 01:46:19,714 [DEBUG] [exchanges.okx_exchange] OKX请求数据: {"instId": "AI16Z-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": "net"}
2025-07-31 01:46:19,778 [DEBUG] [exchanges.gate_exchange] Gate.io请求: POST https://api.gateio.ws/api/v4/futures/usdt/positions/DOT_USDT/leverage?leverage=3, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '22f6bb748fd7c701b378abf53c03802367a8343c02d677dfa1ec1ff11a6910a1f7943a61ec5c573a35362e7c4313f0e1f8c235db7ea5f904965ebc5f2b5cf89d', 'Timestamp': '**********.7783093', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:19,779 [DEBUG] [exchanges.gate_exchange] Gate.io请求: POST https://api.gateio.ws/api/v4/futures/usdt/positions/MATIC_USDT/leverage?leverage=3, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '758af1759fb6ae4f93329594ac814c657e63b4086be9adb40b7b30a2838d6c6bfc5e3ec4370b5cf5d47c6c80c096b06c819e4d588b7fa086208583f385da13d2', 'Timestamp': '**********.7797778', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:19,780 [DEBUG] [exchanges.gate_exchange] Gate.io请求: POST https://api.gateio.ws/api/v4/futures/usdt/positions/SOL_USDT/leverage?leverage=3, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '42dee5c8688d83bf00c717938ad80a315e30b36bc763e37f244af3e4a07b1c9633a8f19d496e19c09236c93e78f12785b9c4df43a4e381db643e25fb82fccbc7', 'Timestamp': '**********.7802877', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:19,781 [DEBUG] [exchanges.gate_exchange] Gate.io请求: POST https://api.gateio.ws/api/v4/futures/usdt/positions/JUP_USDT/leverage?leverage=3, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'f7e83cd3821a264263f2c363707fd3dfd7070b53baa408342b18c30e1a9b3381bdeb63830e70762a537f616f63014f1601f4d0c0f3102640b771b0d8a8ddb824', 'Timestamp': '**********.781367', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:19,831 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'JUP_USDT', 'entry_price': '0', 'mark_price': '0.5249', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.005', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '100', 'history_pnl': '0', 'risk_limit': '1000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897584, 'update_id': 24, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:46:19,832 [INFO] [core.unified_leverage_manager] ✅ Gate.io杠杆设置成功: JUP-USDT 3倍
2025-07-31 01:46:19,834 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'WIF-USDT-SWAP', 'lever': '3', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:46:19,835 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:46:19,835 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:46:19,835 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 1.171秒
2025-07-31 01:46:19,836 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:46:19,837 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:46:19,837 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:46:19,837 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:46:19,838 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/set-leverage
2025-07-31 01:46:19,838 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:46:19,838 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:46:19,839 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:46:19,839 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 1.176秒
2025-07-31 01:46:19,841 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'RESOLV-USDT-SWAP', 'lever': '3', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:46:19,842 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:46:19,843 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:46:19,843 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 1.176秒
2025-07-31 01:46:19,843 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'DOT-USDT-SWAP', 'lever': '3', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:46:19,844 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:46:19,844 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:46:19,844 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 1.187秒
2025-07-31 01:46:19,845 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'SOL-USDT-SWAP', 'lever': '3', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:46:19,845 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:46:19,846 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:46:19,846 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 1.185秒
2025-07-31 01:46:19,846 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:46:19,847 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:46:19,847 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:46:19,847 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:46:19,848 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/set-leverage
2025-07-31 01:46:19,848 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:46:19,849 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:46:19,849 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:46:19,849 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 1.183秒
2025-07-31 01:46:19,849 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'JUP-USDT-SWAP', 'lever': '3', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:46:19,850 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:46:19,850 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:46:19,850 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 1.192秒
2025-07-31 01:46:19,850 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'MATIC_USDT', 'entry_price': '0', 'mark_price': '0.4233', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.08', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '10', 'history_pnl': '0', 'risk_limit': '1000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897584, 'update_id': 32, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:46:19,851 [INFO] [core.unified_leverage_manager] ✅ Gate.io杠杆设置成功: MATIC-USDT 3倍
2025-07-31 01:46:19,851 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'SOL_USDT', 'entry_price': '0', 'mark_price': '179.01', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.005', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '100', 'history_pnl': '0', 'risk_limit': '200000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897584, 'update_id': 32, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:46:19,852 [INFO] [core.unified_leverage_manager] ✅ Gate.io杠杆设置成功: SOL-USDT 3倍
2025-07-31 01:46:19,853 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'DOT_USDT', 'entry_price': '0', 'mark_price': '3.821', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.0066', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '75', 'history_pnl': '0', 'risk_limit': '50000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897584, 'update_id': 32, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:46:19,853 [INFO] [core.unified_leverage_manager] ✅ Gate.io杠杆设置成功: DOT-USDT 3倍
2025-07-31 01:46:19,854 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '0', 'data': [{'instId': 'AI16Z-USDT-SWAP', 'lever': '3', 'mgnMode': 'cross', 'posSide': ''}], 'msg': ''}
2025-07-31 01:46:19,854 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:46:19,854 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:46:19,855 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 1.194秒
2025-07-31 01:46:19,883 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:46:19,883 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'WIFUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:19,884 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:46:19,884 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'ICNTUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:19,885 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:46:19,885 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'RESOLVUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:19,886 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:46:19,886 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'DOTUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:19,887 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:46:19,887 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'AI16ZUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:19,887 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:46:19,888 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'SOLUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:19,889 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:46:19,889 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'JUPUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:19,889 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:46:19,890 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'CAKEUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:19,890 [DEBUG] [bybit_exchange] 🔧 官方SDK标准参数处理: 4 → 4 参数
2025-07-31 01:46:19,890 [DEBUG] [exchanges.bybit_exchange] 🔧 杠杆设置API使用官方SDK标准处理: {'category': 'linear', 'symbol': 'MATICUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:19,892 [DEBUG] [exchanges.okx_exchange] OKX响应: {'code': '51001', 'data': [], 'msg': "Instrument ID or Spread ID doesn't exist."}
2025-07-31 01:46:19,893 [DEBUG] [exchanges.okx_exchange] OKX交易对不存在 - 通用系统正常情况:
2025-07-31 01:46:19,893 [DEBUG] [exchanges.okx_exchange]   - 错误代码: 51001
2025-07-31 01:46:19,893 [DEBUG] [exchanges.okx_exchange]   - 错误信息: Instrument ID or Spread ID doesn't exist.
2025-07-31 01:46:19,893 [DEBUG] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/set-leverage
2025-07-31 01:46:19,894 [DEBUG] [exchanges.okx_exchange] 这是通用系统的正常情况，返回空数据让上层优雅处理
2025-07-31 01:46:19,894 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 01:46:19,894 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 01:46:19,894 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 1.235秒
2025-07-31 01:46:19,925 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: *************198959 -> *************ms
2025-07-31 01:46:19,926 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: **********926, 服务器时间: *************, 偏移: 4225ms
2025-07-31 01:46:19,927 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=**********927, 偏移=4225, 调整后=*************
2025-07-31 01:46:19,927 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: *************
2025-07-31 01:46:19,927 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "SPKUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:19,928 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:46:19,928 [DEBUG] [exchanges.bybit_exchange]   timestamp: *************
2025-07-31 01:46:19,928 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:46:19,928 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:46:19,929 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "SPKUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:19,929 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 119
2025-07-31 01:46:19,929 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 737e5dc77e13e9ae...
2025-07-31 01:46:19,930 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:46:19,930 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '*************', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '737e5dc77e13e9ae8e73d2e9941ac382ad9839d7f233096b63f1631bb191ab7a'}
2025-07-31 01:46:19,930 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'SPKUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:20,039 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.242秒 (要求: 1.500秒)
2025-07-31 01:46:20,039 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.242秒 (要求: 1.500秒)
2025-07-31 01:46:20,040 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.241秒 (要求: 1.500秒)
2025-07-31 01:46:20,040 [INFO] [api_optimizer] 🕐 gate 健壮冷却等待: 1.241秒 (要求: 1.500秒)
2025-07-31 01:46:20,175 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897584404071129 -> 1753897584404ms
2025-07-31 01:46:20,176 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897580176, 服务器时间: 1753897584404, 偏移: 4228ms
2025-07-31 01:46:20,176 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897580176, 偏移=4228, 调整后=1753897584404
2025-07-31 01:46:20,177 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897584404
2025-07-31 01:46:20,177 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "RESOLVUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,178 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:46:20,179 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897584404
2025-07-31 01:46:20,179 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:46:20,179 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:46:20,180 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "RESOLVUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,180 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 122
2025-07-31 01:46:20,181 [DEBUG] [exchanges.bybit_exchange]   生成的签名: f33cf2463f35a67d...
2025-07-31 01:46:20,181 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:46:20,182 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897584404', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': 'f33cf2463f35a67d4b3f686e9499b0c46f11144ef130c5e34464fef89e9268b5'}
2025-07-31 01:46:20,183 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'RESOLVUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:20,185 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897584407514086 -> 1753897584407ms
2025-07-31 01:46:20,186 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897580186, 服务器时间: 1753897584407, 偏移: 4221ms
2025-07-31 01:46:20,186 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897580186, 偏移=4221, 调整后=1753897584407
2025-07-31 01:46:20,187 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897584407
2025-07-31 01:46:20,187 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "WIFUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,188 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:46:20,188 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897584407
2025-07-31 01:46:20,189 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:46:20,189 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:46:20,190 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "WIFUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,190 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 119
2025-07-31 01:46:20,190 [DEBUG] [exchanges.bybit_exchange]   生成的签名: f440f2203bc51d24...
2025-07-31 01:46:20,191 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:46:20,192 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897584407', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': 'f440f2203bc51d245e32ff77733914dad84a60541a3d40e1115b70865cc2f2da'}
2025-07-31 01:46:20,192 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'WIFUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:20,227 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:46:20,228 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆成功: 3倍
2025-07-31 01:46:20,228 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.091秒
2025-07-31 01:46:20,476 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:46:20,477 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆成功: 3倍
2025-07-31 01:46:20,478 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.077秒
2025-07-31 01:46:20,487 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897584715163708 -> 1753897584715ms
2025-07-31 01:46:20,488 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897580488, 服务器时间: 1753897584715, 偏移: 4227ms
2025-07-31 01:46:20,489 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897580489, 偏移=4227, 调整后=1753897584716
2025-07-31 01:46:20,489 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897584716
2025-07-31 01:46:20,490 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "ICNTUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,490 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:46:20,491 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897584716
2025-07-31 01:46:20,491 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:46:20,491 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:46:20,491 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "ICNTUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,492 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 120
2025-07-31 01:46:20,492 [DEBUG] [exchanges.bybit_exchange]   生成的签名: c722cabb2707074a...
2025-07-31 01:46:20,493 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:46:20,493 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897584716', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': 'c722cabb2707074a5fba30a82a4159a245934c6d5cfbcc59a5e2f78b0bf7d593'}
2025-07-31 01:46:20,494 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'ICNTUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:20,497 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897584721223681 -> 1753897584721ms
2025-07-31 01:46:20,497 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897580497, 服务器时间: 1753897584721, 偏移: 4224ms
2025-07-31 01:46:20,498 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897580498, 偏移=4224, 调整后=1753897584722
2025-07-31 01:46:20,499 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897584722
2025-07-31 01:46:20,499 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "DOTUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,500 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:46:20,500 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897584722
2025-07-31 01:46:20,500 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:46:20,501 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:46:20,502 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "DOTUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,502 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 119
2025-07-31 01:46:20,503 [DEBUG] [exchanges.bybit_exchange]   生成的签名: e7359d0bdc9d1dfd...
2025-07-31 01:46:20,503 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:46:20,503 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897584722', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': 'e7359d0bdc9d1dfdc27f6ebd713432ffb10f910ce0cd5178ade39bfcc2b5a038'}
2025-07-31 01:46:20,504 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'DOTUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:20,593 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897584823059908 -> 1753897584823ms
2025-07-31 01:46:20,593 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897580593, 服务器时间: 1753897584823, 偏移: 4230ms
2025-07-31 01:46:20,594 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897580594, 偏移=4230, 调整后=1753897584824
2025-07-31 01:46:20,595 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897584824
2025-07-31 01:46:20,595 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "AI16ZUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,596 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:46:20,596 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897584824
2025-07-31 01:46:20,597 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:46:20,597 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:46:20,597 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "AI16ZUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,598 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 121
2025-07-31 01:46:20,599 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 22ac43a1836c30ef...
2025-07-31 01:46:20,599 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:46:20,599 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897584824', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '22ac43a1836c30ef018369a4a5d0514c95d715d42484b7437a806f7607cb68a1'}
2025-07-31 01:46:20,600 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'AI16ZUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:20,639 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897584864823894 -> 1753897584864ms
2025-07-31 01:46:20,639 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897580639, 服务器时间: 1753897584864, 偏移: 4225ms
2025-07-31 01:46:20,640 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897580640, 偏移=4225, 调整后=1753897584865
2025-07-31 01:46:20,640 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897584865
2025-07-31 01:46:20,641 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "MATICUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,641 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:46:20,642 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897584865
2025-07-31 01:46:20,642 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:46:20,643 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:46:20,643 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "MATICUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,644 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 121
2025-07-31 01:46:20,644 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 008d058a3d0e5784...
2025-07-31 01:46:20,645 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:46:20,645 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897584865', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '008d058a3d0e5784cc633df51c01120ec04c3b634c89d91c7e9b940f220d21a8'}
2025-07-31 01:46:20,646 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'MATICUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:20,808 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:46:20,808 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆成功: 3倍
2025-07-31 01:46:20,809 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.416秒
2025-07-31 01:46:20,819 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:46:20,819 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆成功: 3倍
2025-07-31 01:46:20,820 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.421秒
2025-07-31 01:46:20,922 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897585154427928 -> 1753897585154ms
2025-07-31 01:46:20,922 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897580922, 服务器时间: 1753897585154, 偏移: 4232ms
2025-07-31 01:46:20,923 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897580923, 偏移=4232, 调整后=1753897585155
2025-07-31 01:46:20,924 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897585155
2025-07-31 01:46:20,924 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "JUPUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,925 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:46:20,925 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897585155
2025-07-31 01:46:20,926 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:46:20,926 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:46:20,926 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "JUPUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,927 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 119
2025-07-31 01:46:20,928 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 0087d7cd360d013d...
2025-07-31 01:46:20,928 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:46:20,929 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897585155', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '0087d7cd360d013dbe7cc8c64c14930245df543c0b790ec501d1fb111bac9c2f'}
2025-07-31 01:46:20,929 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'JUPUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:20,937 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897585167731192 -> 1753897585167ms
2025-07-31 01:46:20,938 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897580938, 服务器时间: 1753897585167, 偏移: 4229ms
2025-07-31 01:46:20,939 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897580939, 偏移=4229, 调整后=1753897585168
2025-07-31 01:46:20,939 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897585168
2025-07-31 01:46:20,940 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "SOLUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,940 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:46:20,940 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897585168
2025-07-31 01:46:20,941 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:46:20,941 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:46:20,942 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "SOLUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,942 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 119
2025-07-31 01:46:20,942 [DEBUG] [exchanges.bybit_exchange]   生成的签名: 8a7c22a871fbe9c3...
2025-07-31 01:46:20,942 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:46:20,944 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897585168', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '8a7c22a871fbe9c32ce5ba7a338e2c1325801bf3d1baced2e4a82a944e97d933'}
2025-07-31 01:46:20,944 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'SOLUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:20,946 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-07-31 01:46:20,947 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-07-31 01:46:20,947 [ERROR] [exchanges.bybit_exchange] ❌ Bybit设置杠杆异常: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-07-31 01:46:20,948 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.552秒
2025-07-31 01:46:20,949 [DEBUG] [exchanges.bybit_exchange] Bybit _sync_time使用timeNano: 1753897585175868428 -> 1753897585175ms
2025-07-31 01:46:20,950 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753897580950, 服务器时间: 1753897585175, 偏移: 4225ms
2025-07-31 01:46:20,950 [DEBUG] [exchanges.bybit_exchange] 时间戳计算: 本地时间=1753897580950, 偏移=4225, 调整后=1753897585175
2025-07-31 01:46:20,951 [DEBUG] [exchanges.bybit_exchange] Bybit固定时间戳: 1753897585175
2025-07-31 01:46:20,951 [DEBUG] [exchanges.bybit_exchange] POST请求JSON参数字符串: {"category": "linear", "symbol": "CAKEUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,952 [DEBUG] [exchanges.bybit_exchange] Bybit签名构建:
2025-07-31 01:46:20,952 [DEBUG] [exchanges.bybit_exchange]   timestamp: 1753897585175
2025-07-31 01:46:20,953 [DEBUG] [exchanges.bybit_exchange]   api_key: lYC8LeR8***
2025-07-31 01:46:20,953 [DEBUG] [exchanges.bybit_exchange]   recv_window: 5000
2025-07-31 01:46:20,954 [DEBUG] [exchanges.bybit_exchange]   param_str: {"category": "linear", "symbol": "CAKEUSDT", "buyLeverage": "3", "sellLeverage": "3"}
2025-07-31 01:46:20,954 [DEBUG] [exchanges.bybit_exchange]   完整签名字符串长度: 120
2025-07-31 01:46:20,955 [DEBUG] [exchanges.bybit_exchange]   生成的签名: c7a91e86639a9c18...
2025-07-31 01:46:20,956 [DEBUG] [exchanges.bybit_exchange] Bybit请求: POST https://api.bybit.com/v5/position/set-leverage
2025-07-31 01:46:20,956 [DEBUG] [exchanges.bybit_exchange] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '1753897585175', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': 'c7a91e86639a9c18890591d5c2565b85f7ae8c0bace0436f84603da7ff3a147e'}
2025-07-31 01:46:20,957 [DEBUG] [exchanges.bybit_exchange] Bybit请求参数: {'category': 'linear', 'symbol': 'CAKEUSDT', 'buyLeverage': '3', 'sellLeverage': '3'}
2025-07-31 01:46:21,099 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:46:21,099 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆成功: 3倍
2025-07-31 01:46:21,100 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.699秒
2025-07-31 01:46:21,202 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:46:21,203 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆成功: 3倍
2025-07-31 01:46:21,204 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.806秒
2025-07-31 01:46:21,240 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:46:21,241 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆成功: 3倍
2025-07-31 01:46:21,241 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.846秒
2025-07-31 01:46:21,257 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:46:21,258 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆成功: 3倍
2025-07-31 01:46:21,258 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 2.860秒
2025-07-31 01:46:21,294 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/positions/MATIC_USDT, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '7598b833c1936296e03f18855b19c7ab6c15dc2b2d2bf41cb9a14f8a2974af136f2eb62644bd46afd254d0377997a054bc7769a9975b289f8f306f76b4a2e5e4', 'Timestamp': '1753897581.2941837', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:21,295 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/positions/SOL_USDT, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '691ac65c460bfc6ca86a35218bb40015e4244ed05b1f24d08ebb64a841641fbcae1a4d619d4d537fbb5ad1169b72731eb5ab22d72c668614d452be59e585e456', 'Timestamp': '1753897581.2958033', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:21,297 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/positions/DOT_USDT, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'b98e0eb3f7bc5f05fa9661a911d84b5181571671a770d9d239f14d8cf0327529eeaae71c888ff0bf92648cec940271e0c1cc51b95db2a0ae536bafef676abbd6', 'Timestamp': '1753897581.2974305', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:21,299 [DEBUG] [exchanges.gate_exchange] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/positions/JUP_USDT, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '7c936a8c771c6012c2de18babe255100726f2d3127622f06a68bb4c4669cf7e9b29cf073d2141260a80c521899bd4681db64c95f8c3f4d005e641f6eab712d54', 'Timestamp': '1753897581.2991562', 'Content-Type': 'application/json'}, Data: 
2025-07-31 01:46:21,350 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'JUP_USDT', 'entry_price': '0', 'mark_price': '0.5249', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.005', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '100', 'history_pnl': '0', 'risk_limit': '1000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897584, 'update_id': 24, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:46:21,351 [INFO] [core.unified_leverage_manager] 🔍 Gate.io杠杆验证: 设置3倍, 实际3倍
2025-07-31 01:46:21,352 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: gate JUP-USDT -> 3x
2025-07-31 01:46:21,352 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 3.084秒
2025-07-31 01:46:21,355 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'SOL_USDT', 'entry_price': '0', 'mark_price': '179.01', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.005', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '100', 'history_pnl': '0', 'risk_limit': '200000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897584, 'update_id': 32, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:46:21,356 [INFO] [core.unified_leverage_manager] 🔍 Gate.io杠杆验证: 设置3倍, 实际3倍
2025-07-31 01:46:21,357 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: gate SOL-USDT -> 3x
2025-07-31 01:46:21,358 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 3.081秒
2025-07-31 01:46:21,359 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'MATIC_USDT', 'entry_price': '0', 'mark_price': '0.4233', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.08', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '10', 'history_pnl': '0', 'risk_limit': '1000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897584, 'update_id': 32, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:46:21,359 [INFO] [core.unified_leverage_manager] 🔍 Gate.io杠杆验证: 设置3倍, 实际3倍
2025-07-31 01:46:21,360 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: gate MATIC-USDT -> 3x
2025-07-31 01:46:21,360 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 3.090秒
2025-07-31 01:46:21,362 [DEBUG] [exchanges.gate_exchange] Gate.io API响应成功 (状态码200): {'value': '0', 'leverage': '3', 'mode': 'single', 'realised_point': '0', 'contract': 'DOT_USDT', 'entry_price': '0', 'mark_price': '3.821', 'history_point': '0', 'realised_pnl': '0', 'close_order': None, 'size': 0, 'cross_leverage_limit': '0', 'pending_orders': 0, 'adl_ranking': 6, 'maintenance_rate': '0.0066', 'unrealised_pnl': '0', 'pnl_pnl': '0', 'pnl_fee': '0', 'pnl_fund': '0', 'user': ********, 'leverage_max': '75', 'history_pnl': '0', 'risk_limit': '50000', 'margin': '0', 'last_close_pnl': '0', 'liq_price': '0', 'update_time': 1753897584, 'update_id': 32, 'initial_margin': '0', 'maintenance_margin': '0', 'open_time': 0, 'trade_max_size': '0', 'risk_limit_table': '', 'average_maintenance_rate': '0', 'voucher_size': '0', 'voucher_margin': '0', 'voucher_id': 0}
2025-07-31 01:46:21,363 [INFO] [core.unified_leverage_manager] 🔍 Gate.io杠杆验证: 设置3倍, 实际3倍
2025-07-31 01:46:21,363 [DEBUG] [core.unified_leverage_manager] 💾 杠杆设置已缓存: gate DOT-USDT -> 3x
2025-07-31 01:46:21,364 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 3.092秒
2025-07-31 01:46:21,533 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-31 01:46:21,533 [INFO] [exchanges.bybit_exchange] Bybit设置杠杆成功: 3倍
2025-07-31 01:46:21,534 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 3.140秒
2025-07-31 01:46:21,535 [INFO] [api_optimizer] ✅ 杠杆设置完成: 30/30
2025-07-31 01:46:21,535 [INFO] [api_optimizer] ✅ 分批预加载API调用优化完成
2025-07-31 01:46:21,536 [INFO] [core.trading_system_initializer] ✅ API调用优化完成
2025-07-31 01:46:21,536 [INFO] [core.trading_system_initializer] 🧪 测试API限速效果...
2025-07-31 01:46:21,536 [INFO] [api_optimizer] 🧪 测试gate限速效果，调用次数: 3
2025-07-31 01:46:21,558 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.021秒
2025-07-31 01:46:21,558 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.104秒 (间隔要求: 0.125秒)
2025-07-31 01:46:21,683 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.016秒
2025-07-31 01:46:21,683 [DEBUG] [api_optimizer] 🕐 gate API限速等待: 0.109秒 (间隔要求: 0.125秒)
2025-07-31 01:46:21,823 [DEBUG] [api_optimizer] ✅ gate API调用成功，耗时: 0.016秒
2025-07-31 01:46:21,823 [INFO] [api_optimizer] 📊 gate限速测试结果:
2025-07-31 01:46:21,824 [INFO] [api_optimizer]    总耗时: 0.285秒 (预期最小: 0.250秒)
2025-07-31 01:46:21,824 [INFO] [api_optimizer]    平均间隔: 0.143秒 (预期: 0.125秒)
2025-07-31 01:46:21,825 [INFO] [api_optimizer]    限速有效: ✅
2025-07-31 01:46:21,825 [INFO] [api_optimizer] 🧪 测试bybit限速效果，调用次数: 3
2025-07-31 01:46:21,838 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.012秒
2025-07-31 01:46:21,838 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.238秒 (间隔要求: 0.250秒)
2025-07-31 01:46:22,104 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.016秒
2025-07-31 01:46:22,105 [DEBUG] [api_optimizer] 🕐 bybit API限速等待: 0.234秒 (间隔要求: 0.250秒)
2025-07-31 01:46:22,364 [DEBUG] [api_optimizer] ✅ bybit API调用成功，耗时: 0.016秒
2025-07-31 01:46:22,364 [INFO] [api_optimizer] 📊 bybit限速测试结果:
2025-07-31 01:46:22,365 [INFO] [api_optimizer]    总耗时: 0.539秒 (预期最小: 0.500秒)
2025-07-31 01:46:22,365 [INFO] [api_optimizer]    平均间隔: 0.269秒 (预期: 0.250秒)
2025-07-31 01:46:22,366 [INFO] [api_optimizer]    限速有效: ✅
2025-07-31 01:46:22,366 [INFO] [api_optimizer] 🧪 测试okx限速效果，调用次数: 3
2025-07-31 01:46:22,380 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.012秒
2025-07-31 01:46:22,380 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.488秒 (间隔要求: 0.500秒)
2025-07-31 01:46:22,892 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.015秒
2025-07-31 01:46:22,893 [DEBUG] [api_optimizer] 🕐 okx API限速等待: 0.485秒 (间隔要求: 0.500秒)
2025-07-31 01:46:23,410 [DEBUG] [api_optimizer] ✅ okx API调用成功，耗时: 0.016秒
2025-07-31 01:46:23,410 [INFO] [api_optimizer] 📊 okx限速测试结果:
2025-07-31 01:46:23,411 [INFO] [api_optimizer]    总耗时: 1.043秒 (预期最小: 1.000秒)
2025-07-31 01:46:23,411 [INFO] [api_optimizer]    平均间隔: 0.521秒 (预期: 0.500秒)
2025-07-31 01:46:23,412 [INFO] [api_optimizer]    限速有效: ✅
2025-07-31 01:46:23,413 [INFO] [core.trading_system_initializer] ⏳ 智能API限速冷却...
2025-07-31 01:46:23,413 [INFO] [core.trading_system_initializer] 📊 冷却时间计算: 基础1.0秒 + 缓冲3.0秒 = 总计4.0秒
2025-07-31 01:46:27,430 [INFO] [core.trading_system_initializer] 📡 步骤3: 智能依赖检查和WebSocket启动...
2025-07-31 01:46:27,430 [INFO] [core.trading_system_initializer] 🔍 检查WebSocket启动依赖...
2025-07-31 01:46:27,431 [INFO] [core.trading_system_initializer] ✅ 交易对信息就绪: 10个
2025-07-31 01:46:30,871 [WARNING] [core.trading_system_initializer] ⚠️ 网络连接检查失败: Cannot connect to host httpbin.org:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1122)')]
2025-07-31 01:46:30,990 [INFO] [core.trading_system_initializer] ✅ WebSocket客户端类可用
2025-07-31 01:46:30,994 [INFO] [websocket.manager] WebSocket管理器已创建
2025-07-31 01:46:30,995 [INFO] [websocket.manager] ✅ WebSocketManager logger属性初始化完成
2025-07-31 01:46:31,027 [INFO] [websocket.manager] ✅ WebSocket性能监控器初始化完成
2025-07-31 01:46:31,028 [INFO] [UnifiedConnectionPoolManager] ✅ 统一连接池管理器初始化完成
2025-07-31 01:46:31,028 [INFO] [UnifiedConnectionPoolManager]    🔧 最大连接数: 12
2025-07-31 01:46:31,028 [INFO] [UnifiedConnectionPoolManager]    🔧 监控间隔: 5.0秒
2025-07-31 01:46:31,029 [INFO] [UnifiedConnectionPoolManager]    🔧 重启间隔: 24.0小时
2025-07-31 01:46:31,029 [INFO] [UnifiedConnectionPoolManager] ✅ 配置gate spot端点: 2个
2025-07-31 01:46:31,029 [INFO] [UnifiedConnectionPoolManager] ✅ 配置gate futures端点: 2个
2025-07-31 01:46:31,029 [INFO] [UnifiedConnectionPoolManager] ✅ 配置bybit spot端点: 2个
2025-07-31 01:46:31,030 [INFO] [UnifiedConnectionPoolManager] ✅ 配置bybit futures端点: 2个
2025-07-31 01:46:31,030 [INFO] [UnifiedConnectionPoolManager] ✅ 配置okx spot端点: 2个
2025-07-31 01:46:31,030 [INFO] [UnifiedConnectionPoolManager] ✅ 配置okx futures端点: 2个
2025-07-31 01:46:31,030 [INFO] [websocket.manager] ✅ 统一连接池管理器集成完成
2025-07-31 01:46:31,030 [INFO] [core.trading_system_initializer] ✅ WebSocket管理器可用
2025-07-31 01:46:31,031 [INFO] [core.trading_system_initializer] ✅ API限速冷却完成
2025-07-31 01:46:31,031 [ERROR] [core.trading_system_initializer] ❌ WebSocket启动条件未满足: ['网络连接']
