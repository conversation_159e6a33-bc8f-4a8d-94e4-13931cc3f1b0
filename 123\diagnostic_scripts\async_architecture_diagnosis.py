#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步调用架构问题精确诊断脚本
根据修复提示词要求，精准定位交易规则获取的异步调用架构问题
"""

import os
import sys
import time
import json
import asyncio
import traceback
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def setup_logging():
    """设置日志"""
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('async_architecture_diagnosis.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

class AsyncArchitectureDiagnostic:
    """异步调用架构诊断器"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "diagnosis_type": "异步调用架构问题诊断",
            "problems_found": [],
            "root_causes": [],
            "recommendations": [],
            "test_results": {}
        }
    
    def diagnose_global_exchanges_issue(self):
        """诊断全局交易所实例问题"""
        self.logger.info("🔍 诊断1: 全局交易所实例管理")
        
        try:
            # 检查全局交易所实例管理函数
            from core.trading_system_initializer import get_global_exchanges, set_global_exchanges
            
            # 测试全局交易所实例状态
            current_exchanges = get_global_exchanges()
            self.logger.info(f"当前全局交易所实例: {current_exchanges}")
            
            if current_exchanges is None:
                self.results["problems_found"].append({
                    "problem": "全局交易所实例为None",
                    "severity": "CRITICAL",
                    "description": "get_global_exchanges()返回None，导致交易规则预加载器无法获取交易所实例"
                })
                self.results["root_causes"].append("系统启动时未调用set_global_exchanges()设置全局交易所实例")
            else:
                self.logger.info(f"✅ 全局交易所实例已设置: {list(current_exchanges.keys()) if isinstance(current_exchanges, dict) else type(current_exchanges)}")
                
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 全局交易所实例诊断失败: {e}")
            self.results["problems_found"].append({
                "problem": "全局交易所实例管理函数导入失败",
                "severity": "CRITICAL", 
                "description": str(e)
            })
            return False
    
    def diagnose_trading_rules_preloader(self):
        """诊断交易规则预加载器的异步调用问题"""
        self.logger.info("🔍 诊断2: 交易规则预加载器异步调用架构")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试同步调用get_trading_rule方法
            self.logger.info("测试同步调用get_trading_rule方法...")
            test_result = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            
            if test_result is None:
                self.results["problems_found"].append({
                    "problem": "交易规则获取失败",
                    "severity": "HIGH",
                    "description": "get_trading_rule方法返回None，可能是异步调用架构问题"
                })
                
                # 进一步诊断：检查是否是异步调用问题
                self.logger.info("进一步诊断：检查异步调用逻辑...")
                
                # 检查是否在异步环境中运行
                try:
                    loop = asyncio.get_running_loop()
                    self.logger.info(f"当前运行在异步环境中: {loop}")
                    self.results["problems_found"].append({
                        "problem": "异步环境中调用同步方法",
                        "severity": "HIGH",
                        "description": "在异步环境中调用get_trading_rule同步方法，可能导致事件循环冲突"
                    })
                    self.results["root_causes"].append("异步调用逻辑的设计缺陷：在异步环境中无法正确调用API获取真实规则")
                except RuntimeError:
                    self.logger.info("当前不在异步环境中运行")
                    
            else:
                self.logger.info(f"✅ 交易规则获取成功: {test_result}")
                
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 交易规则预加载器诊断失败: {e}")
            self.results["problems_found"].append({
                "problem": "交易规则预加载器诊断失败",
                "severity": "CRITICAL",
                "description": str(e)
            })
            return False
    
    def diagnose_default_precision_issue(self):
        """诊断默认精度信息问题"""
        self.logger.info("🔍 诊断3: 默认精度信息问题")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试默认精度信息获取
            default_info_bybit = preloader._get_default_precision_info("bybit")
            default_info_gate = preloader._get_default_precision_info("gate")
            default_info_okx = preloader._get_default_precision_info("okx")
            
            self.logger.info(f"Bybit默认精度: {default_info_bybit}")
            self.logger.info(f"Gate默认精度: {default_info_gate}")
            self.logger.info(f"OKX默认精度: {default_info_okx}")
            
            # 检查默认步长是否正确
            if default_info_bybit and default_info_bybit.get("step_size") == 0.001:
                self.results["problems_found"].append({
                    "problem": "Bybit默认步长错误",
                    "severity": "MEDIUM",
                    "description": "Bybit默认步长0.001与真实步长（ICNT=1, SPK=0.1）不符"
                })
                self.results["root_causes"].append("默认值错误：Bybit的默认步长0.001与真实步长不符")
                
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 默认精度信息诊断失败: {e}")
            self.results["problems_found"].append({
                "problem": "默认精度信息诊断失败",
                "severity": "MEDIUM",
                "description": str(e)
            })
            return False
    
    def diagnose_temporary_instance_creation(self):
        """诊断临时实例创建机制"""
        self.logger.info("🔍 诊断4: 临时实例创建机制")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试临时实例创建
            gate_instance = preloader._create_temporary_exchange_instance_sync("gate")
            bybit_instance = preloader._create_temporary_exchange_instance_sync("bybit")
            okx_instance = preloader._create_temporary_exchange_instance_sync("okx")
            
            self.logger.info(f"Gate临时实例: {gate_instance}")
            self.logger.info(f"Bybit临时实例: {bybit_instance}")
            self.logger.info(f"OKX临时实例: {okx_instance}")
            
            # 检查API密钥配置
            api_keys_configured = 0
            api_keys_total = 7  # GATE_API_KEY, GATE_API_SECRET, BYBIT_API_KEY, BYBIT_API_SECRET, OKX_API_KEY, OKX_API_SECRET, OKX_API_PASSPHRASE
            
            for key in ["GATE_API_KEY", "GATE_API_SECRET", "BYBIT_API_KEY", "BYBIT_API_SECRET", "OKX_API_KEY", "OKX_API_SECRET", "OKX_API_PASSPHRASE"]:
                if os.getenv(key):
                    api_keys_configured += 1
                    
            self.logger.info(f"API密钥配置情况: {api_keys_configured}/{api_keys_total}")
            
            if api_keys_configured == 0:
                self.results["problems_found"].append({
                    "problem": "API密钥配置缺失",
                    "severity": "HIGH",
                    "description": f"缺少API密钥配置（{api_keys_configured}/{api_keys_total}个配置）"
                })
                self.results["root_causes"].append("环境变量问题：缺少API密钥配置")
                
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 临时实例创建诊断失败: {e}")
            self.results["problems_found"].append({
                "problem": "临时实例创建诊断失败",
                "severity": "HIGH",
                "description": str(e)
            })
            return False
    
    async def diagnose_async_vs_sync_calls(self):
        """诊断异步vs同步调用问题"""
        self.logger.info("🔍 诊断5: 异步vs同步调用问题")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试在异步环境中调用同步方法
            self.logger.info("在异步环境中测试同步调用...")
            sync_result = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            self.logger.info(f"异步环境中同步调用结果: {sync_result}")
            
            if sync_result is None:
                self.results["problems_found"].append({
                    "problem": "异步环境中同步调用失败",
                    "severity": "CRITICAL",
                    "description": "在异步环境中调用get_trading_rule方法失败，可能是事件循环冲突"
                })
                self.results["root_causes"].append("异步调用逻辑的设计缺陷：在异步环境中无法正确调用API获取真实规则")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 异步vs同步调用诊断失败: {e}")
            self.results["problems_found"].append({
                "problem": "异步vs同步调用诊断失败",
                "severity": "CRITICAL",
                "description": str(e)
            })
            return False
    
    def generate_recommendations(self):
        """生成修复建议"""
        self.logger.info("🔧 生成修复建议...")
        
        recommendations = []
        
        # 基于发现的问题生成建议
        for problem in self.results["problems_found"]:
            if "全局交易所实例" in problem["problem"]:
                recommendations.append({
                    "issue": problem["problem"],
                    "solution": "在initialize_all_systems()方法中添加set_global_exchanges(self.exchanges)调用",
                    "priority": "HIGH",
                    "file": "core/trading_system_initializer.py"
                })
            elif "异步环境" in problem["problem"]:
                recommendations.append({
                    "issue": problem["problem"],
                    "solution": "修复异步调用架构，避免在异步环境中调用asyncio.run()导致事件循环冲突",
                    "priority": "CRITICAL",
                    "file": "core/trading_rules_preloader.py"
                })
            elif "默认步长" in problem["problem"]:
                recommendations.append({
                    "issue": problem["problem"],
                    "solution": "修正默认精度信息，确保与真实API返回的步长一致",
                    "priority": "MEDIUM",
                    "file": "core/trading_rules_preloader.py"
                })
            elif "API密钥" in problem["problem"]:
                recommendations.append({
                    "issue": problem["problem"],
                    "solution": "配置.env文件中的API密钥，或实现无API密钥的兜底机制",
                    "priority": "MEDIUM",
                    "file": ".env"
                })
        
        self.results["recommendations"] = recommendations
    
    def run_diagnosis(self):
        """运行完整诊断"""
        self.logger.info("🚀 开始异步调用架构问题诊断...")
        
        # 同步诊断
        self.diagnose_global_exchanges_issue()
        self.diagnose_trading_rules_preloader()
        self.diagnose_default_precision_issue()
        self.diagnose_temporary_instance_creation()
        
        # 异步诊断
        try:
            asyncio.run(self.diagnose_async_vs_sync_calls())
        except Exception as e:
            self.logger.error(f"异步诊断失败: {e}")
        
        # 生成建议
        self.generate_recommendations()
        
        # 保存结果
        self.save_results()
        
        return self.results
    
    def save_results(self):
        """保存诊断结果"""
        results_file = "async_architecture_diagnosis_results.json"
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            self.logger.info(f"✅ 诊断结果已保存到: {results_file}")
        except Exception as e:
            self.logger.error(f"❌ 保存诊断结果失败: {e}")

def main():
    """主函数"""
    print("🔍 异步调用架构问题精确诊断")
    print("=" * 60)
    
    diagnostic = AsyncArchitectureDiagnostic()
    results = diagnostic.run_diagnosis()
    
    # 输出摘要
    print(f"\n📊 诊断摘要:")
    print(f"发现问题数量: {len(results['problems_found'])}")
    print(f"根本原因数量: {len(results['root_causes'])}")
    print(f"修复建议数量: {len(results['recommendations'])}")
    
    if results['problems_found']:
        print(f"\n🚨 发现的问题:")
        for i, problem in enumerate(results['problems_found'], 1):
            print(f"{i}. [{problem['severity']}] {problem['problem']}")
            print(f"   描述: {problem['description']}")
    
    if results['root_causes']:
        print(f"\n🔍 根本原因:")
        for i, cause in enumerate(results['root_causes'], 1):
            print(f"{i}. {cause}")
    
    if results['recommendations']:
        print(f"\n🔧 修复建议:")
        for i, rec in enumerate(results['recommendations'], 1):
            print(f"{i}. [{rec['priority']}] {rec['issue']}")
            print(f"   解决方案: {rec['solution']}")
            print(f"   文件: {rec['file']}")
    
    print(f"\n✅ 诊断完成，详细结果已保存到JSON文件")
    return results

if __name__ == "__main__":
    main()
