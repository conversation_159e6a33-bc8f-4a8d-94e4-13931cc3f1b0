#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确错误重现脚本
直接重现用户报告的错误：❌ 无法获取交易规则: SPK-USDT_gate_spot
不猜测，不推理，直接重现和定位
"""

import os
import sys
import time
import json
import asyncio
import traceback
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def setup_logging():
    """设置日志"""
    import logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s [%(levelname)s] [%(name)s] %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('precise_error_reproduction.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

class PreciseErrorReproduction:
    """精确错误重现器"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "target_error": "❌ 无法获取交易规则: SPK-USDT_gate_spot",
            "reproduction_steps": [],
            "error_reproduced": False,
            "actual_error_message": "",
            "root_cause_found": False,
            "root_cause_details": {},
            "call_stack": [],
            "system_state": {}
        }
    
    def step1_initialize_system_normally(self):
        """步骤1：正常初始化系统，模拟用户的启动流程"""
        self.logger.info("🔍 步骤1：正常初始化系统")
        
        try:
            # 模拟正常的系统初始化流程
            from core.trading_system_initializer import get_trading_system_initializer
            initializer = get_trading_system_initializer()
            
            # 记录初始化前的状态
            from core.trading_system_initializer import get_global_exchanges
            before_init = get_global_exchanges()
            self.results["system_state"]["global_exchanges_before_init"] = str(before_init)
            
            self.results["reproduction_steps"].append({
                "step": "system_initialization",
                "status": "started",
                "global_exchanges_before": str(before_init)
            })
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 步骤1失败: {e}")
            self.results["reproduction_steps"].append({
                "step": "system_initialization", 
                "status": "failed",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            return False
    
    async def step2_initialize_with_exchanges(self):
        """步骤2：使用真实交易所初始化"""
        self.logger.info("🔍 步骤2：使用真实交易所初始化")
        
        try:
            from core.trading_system_initializer import get_trading_system_initializer
            initializer = get_trading_system_initializer()
            
            # 初始化交易所
            exchanges = await initializer.initialize_exchanges()
            self.results["system_state"]["exchanges_initialized"] = list(exchanges.keys()) if exchanges else []
            
            # 检查全局实例设置
            success = await initializer.initialize_all_systems()
            
            # 检查全局实例状态
            from core.trading_system_initializer import get_global_exchanges
            after_init = get_global_exchanges()
            self.results["system_state"]["global_exchanges_after_init"] = str(type(after_init)) if after_init else "None"
            
            self.results["reproduction_steps"].append({
                "step": "initialize_with_exchanges",
                "status": "completed",
                "success": success,
                "exchanges_count": len(exchanges) if exchanges else 0,
                "global_exchanges_after": str(type(after_init)) if after_init else "None"
            })
            
            return success, exchanges
            
        except Exception as e:
            self.logger.error(f"❌ 步骤2失败: {e}")
            self.results["reproduction_steps"].append({
                "step": "initialize_with_exchanges",
                "status": "failed", 
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            return False, {}
    
    def step3_reproduce_exact_error(self):
        """步骤3：精确重现用户报告的错误"""
        self.logger.info("🔍 步骤3：精确重现 SPK-USDT_gate_spot 错误")
        
        try:
            # 直接调用交易规则预加载器，重现用户的调用场景
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 记录调用前的状态
            cache_before = len(preloader.trading_rules)
            self.results["system_state"]["cache_size_before"] = cache_before
            
            # 精确重现用户的调用：SPK-USDT_gate_spot
            self.logger.info("🎯 精确调用: get_trading_rule('gate', 'SPK-USDT', 'spot')")
            
            # 捕获所有日志输出
            import logging
            log_capture = []
            
            class LogCapture(logging.Handler):
                def emit(self, record):
                    log_capture.append(self.format(record))
            
            capture_handler = LogCapture()
            capture_handler.setLevel(logging.DEBUG)
            
            # 添加到所有相关的logger
            loggers_to_capture = [
                'core.trading_rules_preloader',
                'exchanges.gate_exchange',
                'core.universal_token_system'
            ]
            
            for logger_name in loggers_to_capture:
                logger = logging.getLogger(logger_name)
                logger.addHandler(capture_handler)
            
            try:
                # 执行精确的调用
                result = preloader.get_trading_rule('gate', 'SPK-USDT', 'spot')
                
                # 记录结果
                self.results["reproduction_steps"].append({
                    "step": "exact_error_reproduction",
                    "status": "completed",
                    "result": str(result) if result else "None",
                    "result_type": str(type(result)),
                    "cache_size_after": len(preloader.trading_rules),
                    "captured_logs": log_capture[-20:]  # 最后20条日志
                })
                
                # 检查是否重现了错误
                error_reproduced = False
                actual_error = ""
                
                for log_entry in log_capture:
                    if "❌ 无法获取交易规则" in log_entry and "SPK-USDT_gate_spot" in log_entry:
                        error_reproduced = True
                        actual_error = log_entry
                        break
                    elif "❌ 无法获取交易规则" in log_entry:
                        actual_error = log_entry
                
                self.results["error_reproduced"] = error_reproduced
                self.results["actual_error_message"] = actual_error
                
                if error_reproduced:
                    self.logger.info("✅ 成功重现了用户报告的错误！")
                else:
                    self.logger.info(f"🤔 未重现错误，实际结果: {result}")
                
                return result, log_capture
                
            finally:
                # 清理日志捕获器
                for logger_name in loggers_to_capture:
                    logger = logging.getLogger(logger_name)
                    logger.removeHandler(capture_handler)
            
        except Exception as e:
            self.logger.error(f"❌ 步骤3失败: {e}")
            self.results["reproduction_steps"].append({
                "step": "exact_error_reproduction",
                "status": "failed",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            return None, []
    
    def step4_analyze_call_stack(self, log_capture):
        """步骤4：分析调用栈，找到真正的失败点"""
        self.logger.info("🔍 步骤4：分析调用栈")
        
        try:
            # 分析日志中的关键信息
            key_events = []
            
            for log_entry in log_capture:
                if any(keyword in log_entry for keyword in [
                    "缓存未命中", "动态加载", "临时实例", "API调用", "默认精度", "无法获取"
                ]):
                    key_events.append(log_entry)
            
            self.results["call_stack"] = key_events
            
            # 分析失败模式
            failure_patterns = {
                "cache_miss": any("缓存未命中" in log for log in key_events),
                "dynamic_loading": any("动态加载" in log for log in key_events),
                "temp_instance_creation": any("临时实例" in log for log in key_events),
                "api_call_failure": any("API调用失败" in log for log in key_events),
                "default_fallback": any("默认精度" in log for log in key_events)
            }
            
            self.results["root_cause_details"]["failure_patterns"] = failure_patterns
            
            # 确定根本原因
            if failure_patterns["cache_miss"] and not failure_patterns["dynamic_loading"]:
                root_cause = "缓存未命中但动态加载未触发"
            elif failure_patterns["dynamic_loading"] and not failure_patterns["temp_instance_creation"]:
                root_cause = "动态加载触发但临时实例创建失败"
            elif failure_patterns["temp_instance_creation"] and failure_patterns["api_call_failure"]:
                root_cause = "临时实例创建成功但API调用失败"
            elif failure_patterns["default_fallback"]:
                root_cause = "回退到默认精度但仍然失败"
            else:
                root_cause = "未知原因"
            
            self.results["root_cause_details"]["identified_cause"] = root_cause
            self.results["root_cause_found"] = root_cause != "未知原因"
            
            self.logger.info(f"🎯 根本原因分析: {root_cause}")
            
            return root_cause
            
        except Exception as e:
            self.logger.error(f"❌ 步骤4失败: {e}")
            return "分析失败"
    
    async def run_reproduction(self):
        """运行完整的错误重现流程"""
        self.logger.info("🚀 开始精确错误重现...")
        
        # 步骤1：初始化系统
        if not self.step1_initialize_system_normally():
            return self.results
        
        # 步骤2：使用真实交易所初始化
        success, exchanges = await self.step2_initialize_with_exchanges()
        if not success:
            return self.results
        
        # 步骤3：重现精确错误
        result, log_capture = self.step3_reproduce_exact_error()
        
        # 步骤4：分析调用栈
        root_cause = self.step4_analyze_call_stack(log_capture)
        
        # 保存结果
        self.save_results()
        
        return self.results
    
    def save_results(self):
        """保存重现结果"""
        results_file = "precise_error_reproduction_results.json"
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            self.logger.info(f"✅ 错误重现结果已保存到: {results_file}")
        except Exception as e:
            self.logger.error(f"❌ 保存结果失败: {e}")

async def main():
    """主函数"""
    print("🎯 精确错误重现：❌ 无法获取交易规则: SPK-USDT_gate_spot")
    print("=" * 80)
    
    reproducer = PreciseErrorReproduction()
    results = await reproducer.run_reproduction()
    
    # 输出关键结果
    print(f"\n📊 重现结果摘要:")
    print(f"错误是否重现: {'✅ 是' if results['error_reproduced'] else '❌ 否'}")
    print(f"根本原因是否找到: {'✅ 是' if results['root_cause_found'] else '❌ 否'}")
    
    if results['error_reproduced']:
        print(f"实际错误消息: {results['actual_error_message']}")
    
    if results['root_cause_found']:
        print(f"根本原因: {results['root_cause_details']['identified_cause']}")
    
    print(f"执行步骤数: {len(results['reproduction_steps'])}")
    print(f"调用栈事件数: {len(results['call_stack'])}")
    
    print(f"\n✅ 详细结果已保存到JSON文件")
    return results

if __name__ == "__main__":
    asyncio.run(main())
