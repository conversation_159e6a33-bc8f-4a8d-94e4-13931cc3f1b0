{"timestamp": "2025-07-31 01:33:53", "diagnosis_type": "异步调用架构问题诊断", "problems_found": [{"problem": "全局交易所实例为None", "severity": "CRITICAL", "description": "get_global_exchanges()返回None，导致交易规则预加载器无法获取交易所实例"}, {"problem": "Bybit默认步长错误", "severity": "MEDIUM", "description": "Bybit默认步长0.001与真实步长（ICNT=1, SPK=0.1）不符"}], "root_causes": ["系统启动时未调用set_global_exchanges()设置全局交易所实例", "默认值错误：Bybit的默认步长0.001与真实步长不符"], "recommendations": [{"issue": "全局交易所实例为None", "solution": "在initialize_all_systems()方法中添加set_global_exchanges(self.exchanges)调用", "priority": "HIGH", "file": "core/trading_system_initializer.py"}, {"issue": "Bybit默认步长错误", "solution": "修正默认精度信息，确保与真实API返回的步长一致", "priority": "MEDIUM", "file": "core/trading_rules_preloader.py"}], "test_results": {}}