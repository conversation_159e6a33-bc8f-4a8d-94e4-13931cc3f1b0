2025-07-30 17:55:33 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:55:33 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:55:33 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:55:33 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:55:33.648 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 17:55:33.648 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 17:55:33 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 17:55:33 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 17:55:33 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 17:55:34 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 17:55:34.313 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:55:34.791 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:55:34.791 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:55:35.293 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:55:35.293 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:55:35 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 17:55:35.293 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:55:35.793 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:55:35.793 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:55:36.273 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:55:36.274 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:55:36 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 17:55:36.274 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:55:36.777 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:55:36.777 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:55:37.287 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:55:37.287 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:55:37 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 17:55:37.287 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:55:37.800 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:55:37.800 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:55:38.282 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:55:38.282 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:55:38 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 17:55:38 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 17:55:38.775 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:55:39.984 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:55:41.473 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:55:42.485 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:55:43.065 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:55:44.463 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:55:45.038 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:55:45.611 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:56:04.055 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:56:04.055 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:05.056 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:56:05.056 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:06.054 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:56:06.054 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:07.065 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:56:07.065 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:08.054 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:56:08.055 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:09.082 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:56:09.082 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:53.996 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:56:54.505 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:56:54.506 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:56:54.545 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:56:54.557 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-07-30 17:56:57.135 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-07-30 17:56:57.630 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-07-30 17:56:57.632 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-07-30 17:56:57.633 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-07-30 17:56:57.640 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-07-30 17:56:58.642 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:56:59.143 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:56:59.143 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:56:59.143 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:56:59.143 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:56:59.144 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:56:59.144 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:56:59.144 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:56:59.144 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:56:59.144 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:56:59.245 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:56:59.246 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:59.716 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:56:59.716 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:59.717 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:56:59.717 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:59.736 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:56:59.736 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:59.737 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:56:59.737 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:56:59.737 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:56:59.737 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:56:59.737 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:56:59.737 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:56:59.738 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:56:59.738 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:56:59.738 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:56:59.738 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:59.742 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 17:56:59.742 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:59.743 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:56:59.743 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:56:59.744 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:56:59.744 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:56:59.744 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:56:59.745 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:56:59.745 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:56:59.745 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:56:59.745 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:56:59.745 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:56:59.745 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:56:59.745 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:59.745 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:56:59.746 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:56:59.746 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:56:59.746 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:56:59.746 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:56:59.746 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:56:59.746 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:56:59.746 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:56:59.746 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:56:59.747 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:59.748 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:56:59.749 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:56:59.749 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:56:59.749 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:56:59.749 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:56:59.749 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:56:59.749 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:56:59.749 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:56:59.749 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:56:59.750 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:56:59.756 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:56:59.756 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:56:59.756 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:56:59.756 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:56:59.756 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:56:59.757 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:56:59.757 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:56:59.757 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:56:59.757 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:56:59.757 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:57:00.219 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:57:00.219 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:57:00.222 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:57:00.222 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:57:00.223 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:57:00.224 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:57:00.225 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:57:00.225 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:57:00.227 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:57:00.227 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:57:00.230 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:57:00.230 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:57:00.234 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:57:00.235 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:57:00.235 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:57:00.235 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:57:00.242 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:57:00.242 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:57:09.119 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:57:10.477 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:57:11.968 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:57:12.973 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:57:13.558 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:57:14.045 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:57:14.636 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:57:15.229 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:57:19.977 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:57:21.505 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:57:22.974 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:57:23.987 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:57:24.551 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:57:25.093 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:57:25.673 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:57:26.258 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 18:01:18.109 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:18.110 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:18.111 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:18.111 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:18.112 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:18.148 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:18.848 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 18:01:18.849 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 18:01:18.849 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 18:01:18.849 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 18:01:18.849 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 18:01:18.849 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 18:01:18.849 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 18:01:18.849 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 18:01:18.850 [ERROR] [exchanges.okx_exchange] OKX获取账户配置失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 18:01:36.374 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:38.153 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-30 18:01:38.153 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 18:01:38.741 [INFO] [FuturesTrader.okx] 🔍 [DEBUG] 期货market_sell调用: symbol=SPK-USDT, amount=321.995060, slippage=None, disable_split=True
2025-07-30 18:01:38.741 [INFO] [FuturesTrader.okx] 期货市价卖单使用传入的订单簿数据: SPK-USDT, asks=30, bids=30
2025-07-30 18:01:38.741 [INFO] [FuturesTrader.okx] 🎯 期货卖出: SPK-USDT 321.99506 (精度已由统一系统处理)
2025-07-30 18:01:38.742 [INFO] [FuturesTrader.okx] ✅ 期货卖出使用30档纯净执行价格: 0.10936037
2025-07-30 18:01:38.742 [INFO] [FuturesTrader.okx] 🔧 传递position_side参数给期货交易所: short
2025-07-30 18:01:38.742 [INFO] [exchanges.okx_exchange] 🔥 OKX期货强制使用市价单: SPK-USDT sell
2025-07-30 18:01:39.194 [INFO] [exchanges.okx_exchange] 🔧 OKX账户配置: 账户等级=2, 持仓模式=net_mode
2025-07-30 18:01:39.778 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:39.778 [INFO] [exchanges.okx_exchange] 🔧 OKX期货开仓模式: 合约转换 321.99506 -> 3.0
2025-07-30 18:01:39.778 [INFO] [exchanges.okx_exchange] 🔧 OKX期货统一精度处理: 321.99506 -> 3.0
2025-07-30 18:01:39.778 [INFO] [exchanges.okx_exchange] 🔧 OKX单币种保证金模式，设置tdMode=isolated
2025-07-30 18:01:39.778 [INFO] [exchanges.okx_exchange] 🔧 OKX买卖模式，设置posSide: net
2025-07-30 18:01:39.778 [INFO] [exchanges.okx_exchange] 🔍 OKX期货市价单（修复后）: {'instId': 'SPK-USDT-SWAP', 'side': 'sell', 'ordType': 'market', 'sz': '3.0', 'tdMode': 'isolated', 'posSide': 'net'}
2025-07-30 18:01:39.779 [INFO] [exchanges.okx_exchange] 🚀 OKX期货下单请求详细信息:
2025-07-30 18:01:39.779 [INFO] [exchanges.okx_exchange]    原始数量: 321.99506
2025-07-30 18:01:39.779 [INFO] [exchanges.okx_exchange]    格式化数量: 3.0
2025-07-30 18:01:39.779 [INFO] [exchanges.okx_exchange]    API sz参数: '3.0'
2025-07-30 18:01:39.779 [INFO] [exchanges.okx_exchange]    完整请求: {'instId': 'SPK-USDT-SWAP', 'side': 'sell', 'ordType': 'market', 'sz': '3.0', 'tdMode': 'isolated', 'posSide': 'net'}
2025-07-30 18:01:40.315 [INFO] [exchanges.okx_exchange] 🔍 OKX期货下单响应: [{'clOrdId': '', 'ordId': '2730958318718476288', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '', 'ts': '1753891300241'}]
2025-07-30 18:01:40.316 [INFO] [exchanges.okx_exchange] 🎯 OKX期货API执行验证:
2025-07-30 18:01:40.316 [INFO] [exchanges.okx_exchange]    订单ID: 2730958318718476288
2025-07-30 18:01:40.316 [INFO] [exchanges.okx_exchange]    请求sz: '3.0'
2025-07-30 18:01:40.316 [INFO] [exchanges.okx_exchange]    响应数据: {'clOrdId': '', 'ordId': '2730958318718476288', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '', 'ts': '1753891300241'}
2025-07-30 18:01:40.316 [ERROR] [exchanges.okx_exchange] ❌ 检测到数量不匹配!
2025-07-30 18:01:40.316 [ERROR] [exchanges.okx_exchange]    原始数量: 321.99506
2025-07-30 18:01:40.316 [ERROR] [exchanges.okx_exchange]    API数量: 3.0
2025-07-30 18:01:40.316 [ERROR] [exchanges.okx_exchange]    放大倍数: 0.01x
2025-07-30 18:01:40.860 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 18:01:40.861 [ERROR] [exchanges.okx_exchange]   - 错误代码: 51603
2025-07-30 18:01:40.861 [ERROR] [exchanges.okx_exchange]   - 错误信息: Order does not exist
2025-07-30 18:01:40.861 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'code': '51603', 'data': [], 'msg': 'Order does not exist'}
2025-07-30 18:01:40.861 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/trade/order
2025-07-30 18:01:40.861 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 18:01:40.861 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-30 18:01:40.861 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Order does not exist (代码: 51603)',)
2025-07-30 18:01:40.861 [ERROR] [exchanges.okx_exchange] ❌ 获取OKX订单2730958318718476288成交价格失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-30 18:01:40.861 [INFO] [exchanges.okx_exchange] ✅ OKX期货返回: amount=321.99506, executed_price=0.00000000
2025-07-30 18:01:40.862 [WARNING] [FuturesTrader.okx] ⚠️ 无法获取okx期货实际成交价格，使用30档算法价格: 0.10936037
2025-07-30 18:01:40.862 [WARNING] [FuturesTrader.okx] ⚠️ API响应: {'order_id': '2730958318718476288', 'symbol': 'SPK-USDT', 'side': 'sell', 'type': 'market', 'amount': 321.99506, 'actual_amount': 3.0, 'filled': 3.0, 'executed_quantity': 3.0, 'price': 0, 'executed_price': 0.0, 'average': 0.0, 'status': 'open', 'timestamp': 1753891300861}
2025-07-30 18:01:40.862 [INFO] [FuturesTrader.okx] ✅ 使用兜底机制继续执行，确保交易不中断
2025-07-30 18:01:42.818 [INFO] [exchanges.okx_exchange] 🔥 OKX期货强制使用市价单: SPK-USDT buy
2025-07-30 18:01:43.405 [INFO] [exchanges.okx_exchange] 🔧 OKX账户配置: 账户等级=2, 持仓模式=net_mode
2025-07-30 18:01:43.405 [INFO] [exchanges.okx_exchange] 🔧 OKX期货平仓模式: 跳过合约转换，直接使用 3.0
2025-07-30 18:01:43.405 [INFO] [exchanges.okx_exchange] 🔧 OKX期货统一精度处理: 3.0 -> 3.0
2025-07-30 18:01:43.405 [INFO] [exchanges.okx_exchange] 🔧 OKX单币种保证金模式，设置tdMode=isolated
2025-07-30 18:01:43.405 [INFO] [exchanges.okx_exchange] 🔧 OKX买卖模式，设置posSide: net
2025-07-30 18:01:43.405 [INFO] [exchanges.okx_exchange] 🔍 OKX期货市价单（修复后）: {'instId': 'SPK-USDT-SWAP', 'side': 'buy', 'ordType': 'market', 'sz': '3.0', 'tdMode': 'isolated', 'posSide': 'net'}
2025-07-30 18:01:43.405 [INFO] [exchanges.okx_exchange] 🚀 OKX期货下单请求详细信息:
2025-07-30 18:01:43.405 [INFO] [exchanges.okx_exchange]    原始数量: 3.0
2025-07-30 18:01:43.405 [INFO] [exchanges.okx_exchange]    格式化数量: 3.0
2025-07-30 18:01:43.405 [INFO] [exchanges.okx_exchange]    API sz参数: '3.0'
2025-07-30 18:01:43.405 [INFO] [exchanges.okx_exchange]    完整请求: {'instId': 'SPK-USDT-SWAP', 'side': 'buy', 'ordType': 'market', 'sz': '3.0', 'tdMode': 'isolated', 'posSide': 'net'}
2025-07-30 18:01:43.907 [INFO] [exchanges.okx_exchange] 🔍 OKX期货下单响应: [{'clOrdId': '', 'ordId': '2730958439547985920', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '', 'ts': '1753891303842'}]
2025-07-30 18:01:43.907 [INFO] [exchanges.okx_exchange] 🎯 OKX期货API执行验证:
2025-07-30 18:01:43.907 [INFO] [exchanges.okx_exchange]    订单ID: 2730958439547985920
2025-07-30 18:01:43.907 [INFO] [exchanges.okx_exchange]    请求sz: '3.0'
2025-07-30 18:01:43.907 [INFO] [exchanges.okx_exchange]    响应数据: {'clOrdId': '', 'ordId': '2730958439547985920', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '', 'ts': '1753891303842'}
2025-07-30 18:01:43.907 [INFO] [exchanges.okx_exchange] ✅ 数量匹配正确: 3.0 = 3.0
2025-07-30 18:01:44.460 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 18:01:44.461 [ERROR] [exchanges.okx_exchange]   - 错误代码: 51603
2025-07-30 18:01:44.461 [ERROR] [exchanges.okx_exchange]   - 错误信息: Order does not exist
2025-07-30 18:01:44.461 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'code': '51603', 'data': [], 'msg': 'Order does not exist'}
2025-07-30 18:01:44.461 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/trade/order
2025-07-30 18:01:44.461 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 18:01:44.461 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-30 18:01:44.461 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Order does not exist (代码: 51603)',)
2025-07-30 18:01:44.461 [ERROR] [exchanges.okx_exchange] ❌ 获取OKX订单2730958439547985920成交价格失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-30 18:01:44.461 [INFO] [exchanges.okx_exchange] ✅ OKX期货返回: amount=3.0, executed_price=0.00000000
2025-07-30 18:01:46.069 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:46.703 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 18:01:46.703 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:46.704 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:46.705 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 18:01:46.705 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 18:01:46.706 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 18:01:47.638 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 18:01:47.639 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 18:01:47.639 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 18:01:47.639 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 18:01:47.639 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 18:01:47.639 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 18:01:47.639 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 18:01:47.639 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 18:01:47.639 [ERROR] [exchanges.okx_exchange] OKX获取账户配置失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 18:01:48.558 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 18:01:48.559 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 18:01:48.559 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 18:01:48.559 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 18:01:48.559 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 18:01:48.559 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 18:01:48.559 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 18:01:48.559 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 18:01:48.559 [ERROR] [exchanges.okx_exchange] OKX获取账户配置失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 01:20:52 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-31 01:20:52 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-31 01:20:52 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 01:20:52 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-31 01:20:52 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-31 01:20:52 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-31 01:20:52 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-31 01:33:53 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-31 01:33:53 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-31 01:33:53 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 01:33:53 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
