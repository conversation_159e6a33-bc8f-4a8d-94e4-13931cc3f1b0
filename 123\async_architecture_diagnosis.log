2025-07-31 01:33:53,426 [INFO] 🚀 开始异步调用架构问题诊断...
2025-07-31 01:33:53,426 [INFO] 🔍 诊断1: 全局交易所实例管理
2025-07-31 01:33:53,903 [INFO] TelegramNotifier初始化:
2025-07-31 01:33:53,903 [INFO]   Bot Token: 已设置
2025-07-31 01:33:53,903 [INFO]   Chat ID: 已设置
2025-07-31 01:33:53,903 [INFO]   Base URL: https://api.telegram.org/bot7954850653:AAHOIyX61SF1oDDxZBVclzfdmJhX-Jbn0-0/sendMessage
2025-07-31 01:33:53,904 [INFO] ✅ Telegram配置完整，通知功能已启用
2025-07-31 01:33:53,904 [INFO] 已启用的通知器: ['telegram', 'console']
2025-07-31 01:33:53,928 [INFO] 当前全局交易所实例: None
2025-07-31 01:33:53,928 [INFO] 🔍 诊断2: 交易规则预加载器异步调用架构
2025-07-31 01:33:53,930 [INFO] 🚀 通用代币系统初始化: 支持10个代币
2025-07-31 01:33:53,931 [INFO] ✅ 加载交易对缓存: 10个交易对
2025-07-31 01:33:53,931 [INFO] 📅 缓存时间: Thu Jul 31 00:55:45 2025
2025-07-31 01:33:53,932 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 01:33:53,932 [INFO] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 01:33:53,932 [INFO] ✅ 交易规则预加载器初始化完成
2025-07-31 01:33:53,932 [INFO]    缓存过期时间: 24小时
2025-07-31 01:33:53,933 [INFO]    预加载交易对数量: 10
2025-07-31 01:33:53,933 [INFO] 测试同步调用get_trading_rule方法...
2025-07-31 01:33:53,933 [INFO] 🚀 5大缓存系统监控器启动
2025-07-31 01:33:53,934 [INFO] ================================================================================
2025-07-31 01:33:53,934 [INFO] 📋 [交易规则缓存] 未命中: gate SPK-USDT spot - 需要API获取
2025-07-31 01:33:53,934 [WARNING] ⚠️ 交易规则缓存未命中: gate_SPK-USDT_spot
2025-07-31 01:33:53,935 [INFO] 🔄 尝试动态加载交易规则: gate_SPK-USDT_spot
2025-07-31 01:33:53,935 [INFO] 🔄 尝试创建临时交易所实例: gate
2025-07-31 01:33:53,936 [INFO] 初始化gate交易所接口，API请求限制: 10/秒
2025-07-31 01:33:53,936 [INFO] 🔧 Gate API限制根源修复为8次/秒，确保30+代币健壮启动
2025-07-31 01:33:53,936 [INFO] 初始化Gate.io交易所接口，API请求限制: 8/秒
2025-07-31 01:33:53,937 [INFO] ✅ 统一开仓管理器初始化完成
2025-07-31 01:33:53,937 [INFO]    🔥 使用API动态精度，删除硬编码
2025-07-31 01:33:53,937 [INFO]    🔥 步长缓存机制，最高速度
2025-07-31 01:33:53,938 [INFO]    🔥 严格截断，不四舍五入
2025-07-31 01:33:53,938 [INFO] ✅ 统一平仓管理器初始化完成
2025-07-31 01:33:53,938 [INFO]    🔥 API精度+步长+缓存+重试机制
2025-07-31 01:33:53,939 [INFO]    🔥 严格截取，绝不四舍五入
2025-07-31 01:33:53,939 [INFO]    最大重试次数: 3
2025-07-31 01:33:53,939 [INFO]    重试精度序列: [6, 4, 2, 1]
2025-07-31 01:33:53,939 [INFO] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 01:33:53,941 [INFO] ✅ 统一HTTP会话管理器初始化完成
2025-07-31 01:33:53,942 [INFO] ✅ 临时交易所实例创建成功: gate
2025-07-31 01:33:53,942 [INFO] ✅ 同步加载成功: gate_SPK-USDT_spot
2025-07-31 01:33:53,943 [INFO] ✅ 交易规则获取成功: TradingRule(symbol='SPK-USDT', exchange='gate', market_type='spot', qty_step=Decimal('0.0001'), price_step=Decimal('0.01'), qty_precision=4, price_precision=4, min_qty=Decimal('0.0001'), max_qty=Decimal('1000000'), min_notional=Decimal('1.0'), source='default', timestamp=1753896833.9429207)
2025-07-31 01:33:53,943 [INFO] 🔍 诊断3: 默认精度信息问题
2025-07-31 01:33:53,944 [INFO] Bybit默认精度: {'step_size': 0.001, 'min_amount': 0.001, 'max_amount': 1000000, 'price_precision': 2, 'amount_precision': 3, 'min_notional': 5.0, 'source': 'default'}
2025-07-31 01:33:53,944 [INFO] Gate默认精度: {'step_size': 0.0001, 'min_amount': 0.0001, 'max_amount': 1000000, 'price_precision': 4, 'amount_precision': 4, 'min_notional': 1.0, 'source': 'default'}
2025-07-31 01:33:53,945 [INFO] OKX默认精度: {'step_size': 1e-05, 'min_amount': 1e-05, 'max_amount': 1000000, 'price_precision': 5, 'amount_precision': 5, 'min_notional': 1.0, 'source': 'default'}
2025-07-31 01:33:53,945 [INFO] 🔍 诊断4: 临时实例创建机制
2025-07-31 01:33:53,946 [INFO] 初始化gate交易所接口，API请求限制: 10/秒
2025-07-31 01:33:53,946 [INFO] 🔧 Gate API限制根源修复为8次/秒，确保30+代币健壮启动
2025-07-31 01:33:53,947 [INFO] 初始化Gate.io交易所接口，API请求限制: 8/秒
2025-07-31 01:33:53,947 [INFO] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 01:33:53,947 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 01:33:53,948 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 01:33:53,948 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 01:33:53,948 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 01:33:53,952 [INFO] ✅ 统一网络配置管理器初始化完成
2025-07-31 01:33:53,952 [INFO]    🔥 连接超时: 5.0秒
2025-07-31 01:33:53,953 [INFO]    🔥 总超时: 10.0秒
2025-07-31 01:33:53,953 [INFO]    🔥 最大重试: 3次
2025-07-31 01:33:53,953 [INFO]    🔥 重试延迟: 50ms
2025-07-31 01:33:53,954 [INFO] 🔥 网络配置环境变量已应用
2025-07-31 01:33:53,956 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 01:33:53,956 [INFO] ✅ 动态阈值系统已启用，无需传统阈值验证
2025-07-31 01:33:53,957 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 01:33:53,957 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 01:33:53,957 [INFO] 初始化OKX交易所接口，API请求限制: 5/秒
2025-07-31 01:33:53,959 [INFO] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-31 01:33:53,959 [INFO] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-31 01:33:53,959 [INFO] Gate临时实例: <exchanges.gate_exchange.GateExchange object at 0x000001E009CAF100>
2025-07-31 01:33:53,960 [INFO] Bybit临时实例: <exchanges.bybit_exchange.BybitExchange object at 0x000001E009CAF880>
2025-07-31 01:33:53,960 [INFO] OKX临时实例: <exchanges.okx_exchange.OKXExchange object at 0x000001E009CAF8E0>
2025-07-31 01:33:53,960 [INFO] API密钥配置情况: 7/7
2025-07-31 01:33:53,961 [INFO] 🔍 诊断5: 异步vs同步调用问题
2025-07-31 01:33:53,961 [INFO] 在异步环境中测试同步调用...
2025-07-31 01:33:53,962 [INFO] 异步环境中同步调用结果: TradingRule(symbol='SPK-USDT', exchange='gate', market_type='spot', qty_step=Decimal('0.0001'), price_step=Decimal('0.01'), qty_precision=4, price_precision=4, min_qty=Decimal('0.0001'), max_qty=Decimal('1000000'), min_notional=Decimal('1.0'), source='default', timestamp=1753896833.9429207)
2025-07-31 01:33:53,963 [INFO] 🔧 生成修复建议...
2025-07-31 01:33:53,964 [INFO] ✅ 诊断结果已保存到: async_architecture_diagnosis_results.json
